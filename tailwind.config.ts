import type { Config } from 'tailwindcss'
import tailwindAnimate from 'tailwindcss-animate'
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    '*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        // Phương án 1: <PERSON><PERSON><PERSON> cảm hứng từ ZaloPay
        brand: {
          primary: '#0068FF', // Xanh dương đậm hơn (ZaloPay blue)
          secondary: '#E6F0FF', // Xanh dương rất nhạt (background)
          white: '#FFFFFF', // <PERSON><PERSON>u trắng
          gray_light: '#F8F9FC', // <PERSON><PERSON>m nhạt hơn
          gray_medium: '#EEF2F6', // <PERSON>ám trung bình cho borders
          accent: '#00C6FF', // Xanh dương sáng làm màu nhấn
          success: '#00BFA5', // Xanh lá mòng biển (teal)
          warning: '#FFC107', // Vàng cảnh báo
          error: '#FF3B30', // Đỏ lỗi
          text: {
            primary: '#1A1A1A', // Đen nhạt cho text chính
            secondary: '#717171', // Xám đậm cho text phụ
          },
        },

        // Giữ lại các màu mặc định của shadcn/ui
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [tailwindAnimate],
} satisfies Config

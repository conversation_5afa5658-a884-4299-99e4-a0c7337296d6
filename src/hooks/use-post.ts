import { postService } from '@/services/post.service'
import { PostDetail, PostStatus } from '@/types/post-schema.type'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

export interface PhotographerRequestParams {
  postId: string
}

export const usePost = () => {
  const router = useRouter()
  const createPostMutation = useMutation({
    mutationFn: async (postData: PostDetail) => {
      const cleanedPostData = Object.entries(postData).reduce((acc, [key, value]) => {
        if (value !== null && value !== undefined && value !== '' && value !== 0 && !Number.isNaN(value)) {
          acc[key as keyof PostDetail] = value
        }
        return acc
      }, {} as Partial<PostDetail>)
      delete cleanedPostData.id
      return postService.createPost(cleanedPostData as PostDetail)
    },
    onSuccess: () => {
      toast.success('<PERSON><PERSON><PERSON> đăng đã được tạo thành công!')
      router.push('/customer')
    },
  })

  const { data: pendingPosts, refetch } = useQuery({
    queryKey: ['pendingPosts'],
    queryFn: () => postService.getPendingPosts(),
    refetchOnWindowFocus: false,
    // staleTime: 30 * 1000,
  })

  const updatePostStatusMutation = useMutation({
    mutationFn: async (params: { postId: string; status: PostStatus; rejectedReason?: string }) => {
      const { postId, status, rejectedReason } = params
      return postService.updatePostStatus(postId, status, rejectedReason)
    },
    onSuccess: () => {
      toast.success('Trạng thái bài đăng đã được cập nhật!')
      refetch()
    },
    onError: () => {
      toast.error('Cập nhật trạng thái thất bại!')
    },
  })

  const useFindAllPosts = (params?: {
    search?: string
    limit?: number
    page?: number
    sort?: string
    direction?: 'asc' | 'desc'
    includeDetails?: boolean
    status: PostStatus | 'ALL'
    accountId?: string
  }) => {
    return useQuery({
      queryKey: ['sorted-posts', params],
      queryFn: () => postService.findSortedPosts(params),
      refetchOnWindowFocus: false,
      // staleTime: 30 * 1000,
    })
  }

  const usePostDetail = (postId: string, photographerId?: string) => {
    return useQuery({
      queryKey: ['post-detail', postId, photographerId],
      queryFn: () => postService.getPostDetail(postId, { ...(photographerId && { photographerId }) }),
      refetchOnWindowFocus: false,
      // staleTime: 30 * 1000,
    })
  }

  const usePhotographerRequest = useMutation({
    mutationFn: ({ postId }: PhotographerRequestParams) => postService.sendPhotographerRequest({ postId }),
    onSuccess: () => {
      console.log('Photographer request sent successfully')
    },
    onError: (error) => {
      console.error('Error sending photographer request:', error.message)
    },
  })

  const usePostsByPhotographer = () => {
    return useQuery({
      queryKey: ['post-ptg'],
      queryFn: () => postService.getPostsByPhotographer(),
      refetchOnWindowFocus: false,
      // staleTime: 30 * 1000,
    })
  }

  const useChoosePhotographer = useMutation({
    mutationFn: ({ postId, requestId }: { postId: string; requestId: string }) =>
      postService.choosePhotographer({ postId, requestId }),
    onSuccess: () => {
      console.log('Photographer chosen successfully')
    },
    onError: (error) => {
      console.error('Error choosing photographer:', error.message)
    },
  })

  const useConfirmBooking = useMutation({
    mutationFn: ({ bookingId }: { bookingId: string }) => postService.confirmBooking({ bookingId }),
    onSuccess: () => {
      console.log('Photographer chosen successfully')
    },
    onError: (error) => {
      console.error('Error choosing photographer:', error.message)
    },
  })

  return {
    createPost: createPostMutation.mutate,
    pendingPosts,
    updatePostStatus: updatePostStatusMutation.mutate,
    isLoading: createPostMutation.isPending,
    isError: createPostMutation.isError,
    error: createPostMutation.error,
    useFindAllPosts,
    usePostDetail,
    usePostsByPhotographer,
    sendRequest: usePhotographerRequest.mutate,
    isRequestPending: usePhotographerRequest.isPending,
    isRequestSuccess: usePhotographerRequest.isSuccess,
    isRequestError: usePhotographerRequest.isError,
    requestError: usePhotographerRequest.error,
    choosePhotographer: useChoosePhotographer.mutate,
    confirmBooking: useConfirmBooking.mutate,
    confirmLoading: useConfirmBooking.isPending,
    confirmSuccess: useConfirmBooking.isSuccess,
  }
}

'use client'

import { authService } from '@/services/auth.service'
import { useUserStore } from '@/stores/auth.store'
import { LoginCredentials, RegisterCredentials } from '@/types/auth.type'
import { useMutation } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

export function useAuth() {
  const router = useRouter()
  const { setAuth, setJwt, clearJwt } = useUserStore()
  const [errorMessageRegister, setErrorMessageRegister] = useState<string | null>(null)
  const [errorMessageLogin, setErrorMessageLogin] = useState<string | null>(null)

  const registerMutation = useMutation({
    mutationFn: (credentials: RegisterCredentials) => authService.register(credentials),
    onSuccess: (data) => {
      setErrorMessageRegister(null)
      setJwt(data?.accessToken)
      setAuth(data?.accessToken, data?.user)
      router.push('/')
    },
    onError: (error) => {
      console.log('Register failed:', error)
      if (error instanceof AxiosError) {
        setErrorMessageRegister(error.response?.data?.message || 'Đăng ký thất bại!')
      } else {
        setErrorMessageRegister('Đăng ký thất bại!')
      }
    },
  })

  const loginMutation = useMutation({
    mutationFn: (credentials: LoginCredentials) => authService.login(credentials),
    onSuccess: (data) => {
      setErrorMessageLogin(null)
      setJwt(data?.accessToken)
      setAuth(data?.accessToken, data?.user)
      router.push('/')
    },
    onError: (error) => {
      let errorMessage = 'Đăng nhập thất bại!'
      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || errorMessage
      }
      console.log('Login failed:', errorMessage)
      setErrorMessageLogin(errorMessage)
    },
  })

  const logoutMutation = useMutation({
    mutationFn: () => authService.logout(false),
    onSuccess: () => {
      clearJwt()
      router.push('/')
    },
    onError: (error) => {
      console.error('Logout failed:', error)
    },
  })

  return {
    register: registerMutation.mutate,
    registerStatus: {
      isLoading: registerMutation.isPending,
      isError: registerMutation.isError,
      error: registerMutation.error,
    },
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    errorMessageLogin,
    errorMessageRegister,
    isLoginPending: loginMutation.isPending,
    isLoginError: loginMutation.isError,
    isLogoutPending: logoutMutation.isPending,
    isLogoutError: logoutMutation.isError,
  }
}

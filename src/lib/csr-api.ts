import { useUserStore } from '@/stores/auth.store'
import axios, { AxiosResponse } from 'axios'

interface RefreshResponse {
  accessToken: string
}

const API_URL = process.env.NEXT_PUBLIC_API_URL

const picnowApi = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request Interceptor
picnowApi.interceptors.request.use(
  (config) => {
    if (typeof window !== 'undefined') {
      const token = useUserStore.getState().jwt
      if (token) config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error),
)

// Response Interceptor for Token Refresh
picnowApi.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error) => {
    const originalRequest = error.config
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      typeof window !== 'undefined' &&
      !originalRequest.url.includes('/auth/login') &&
      !originalRequest.url.includes('/auth/register') &&
      !originalRequest.url.includes('/auth/refresh')
    ) {
      originalRequest._retry = true
      try {
        const { data }: { data: RefreshResponse } = await picnowApi.post('/auth/refresh', {}, { withCredentials: true })
        useUserStore.getState().setJwt(data.accessToken)
        originalRequest.headers.Authorization = `Bearer ${data.accessToken}`
        return picnowApi(originalRequest)
      } catch (refreshError) {
        useUserStore.getState().clearJwt()
        if (typeof window !== 'undefined') {
          window.location.href = '/sign-in'
        }
        return Promise.reject(refreshError)
      }
    }
    const errorData = error.response?.data
    const errorMessage = errorData?.errors?.message || error.message
    error.message = errorMessage
    return Promise.reject(error)
  },
)

// export function getApiInstance() {
//   return typeof window === 'undefined' ? serverApi : picnowApi
// }

export default picnowApi

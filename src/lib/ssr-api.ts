import axios from 'axios'
import { cache } from 'react'

const serverApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

interface ApiResponse<T> {
  data: T
  status: number
}

export const fetchPublicData = cache(
  async <T>(endpoint: string, params?: Record<string, unknown>): Promise<ApiResponse<T>> => {
    try {
      const response = await serverApi.get<T>(endpoint, { params })
      return {
        data: response.data,
        status: response.status,
      }
    } catch (error) {
      console.error(`Error fetching public data from ${endpoint}:`, error)
      throw error
    }
  },
)

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

// Define protected routes
const protectedRoutes = ['/protected', '/bookings']

// Middleware function
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for public routes or API calls
  if (pathname === '/sign-in' || pathname.startsWith('/api')) {
    return NextResponse.next()
  }

  // Check if the route requires authentication
  const isProtected = protectedRoutes.some((route) => pathname.startsWith(route))
  const refreshToken = request.cookies.get('refresh_token')?.value

  if (isProtected) {
    if (!refreshToken) {
      // No refresh token cookie: redirect to login
      return NextResponse.redirect(new URL('/sign-in', request.url))
    }

    // Since access token isn't in a cookie, we can't verify it here.
    // Let the client handle token refresh and redirect if needed.
    // Add a header to signal the client to check auth state.
    const response = NextResponse.next()
    response.headers.set('x-require-auth', 'true')
    return response
  }

  // Public route: proceed without checks
  return NextResponse.next()
}

// Matcher: Apply middleware to all routes
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'],
}

export const phoneRegExp = /^(\+84|0)(\s?)(3[2-9]|5[6|8|9]|7[0|6-9]|8[0-9]|9[0-4|6-9])(\d)(\s?\d{3})(\s?\d{3})$/

export const formatPhoneNumber = (value: string) => {
  let digits = value.replace(/[^\d+]/g, '')

  if (digits.startsWith('+84')) {
    digits = '+84' + digits.slice(3)
  } else if (digits.startsWith('84')) {
    digits = '+84' + digits.slice(2)
  }

  if (digits.startsWith('+84')) {
    if (digits.length <= 3) return digits
    if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`
    if (digits.length <= 9) return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`
  } else {
    if (digits.length <= 1) return digits
    if (digits.length <= 4) return `${digits.slice(0, 4)}`
    if (digits.length <= 7) return `${digits.slice(0, 4)} ${digits.slice(4)}`
    return `${digits.slice(0, 4)} ${digits.slice(4, 7)} ${digits.slice(7, 10)}`
  }
}

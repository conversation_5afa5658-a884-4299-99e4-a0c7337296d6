import { queryClient } from '@/config/query-client'
import '@/styles/fonts.css'
import '@/styles/globals.css'
import { QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'sonner'

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`scroll-none antialiased`}>
        <QueryClientProvider client={queryClient}>
          {children}
          <Toaster richColors />
        </QueryClientProvider>
      </body>
    </html>
  )
}

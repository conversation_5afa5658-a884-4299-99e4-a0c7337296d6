import Footer from '@/components/layout/footer'
import Header from '@/components/layout/header'

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="relative flex flex-1 items-center justify-center overflow-hidden bg-gradient-to-br from-[#4A90E2]/5 via-[#F5F7FA] to-[#F5A623]/5">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -left-10 top-10 h-64 w-64 animate-pulse rounded-full bg-[#4A90E2]/10 blur-3xl" />
          <div className="absolute right-0 top-1/3 h-96 w-96 animate-pulse rounded-full bg-[#F5A623]/10 blur-3xl delay-700" />
          <div className="absolute bottom-0 left-1/3 h-72 w-72 animate-pulse rounded-full bg-[#50C878]/20 blur-3xl delay-1000" />
        </div>
        <div className="relative z-10">{children}</div>
      </main>
      <Footer />
    </div>
  )
}

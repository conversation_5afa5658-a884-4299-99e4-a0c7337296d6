'use client'
import AuthFlow from '@/components/auth/AuthFlow'
import StatsSection from '@/components/auth/StatsSection'
import { motion } from 'framer-motion' // Thư viện animation mạnh mẽ
import { Award, ImageDownIcon, Star, Users } from 'lucide-react'

const AuthLandingPage = () => {
  const cardVariants = {
    hover: { scale: 1.05, boxShadow: '0 10px 20px rgba(74, 144, 226, 0.2)', transition: { duration: 0.3 } },
    initial: { scale: 1, boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)' },
  }

  return (
    <div className="container relative z-10 mx-auto px-4 pb-20 pt-8">
      <div className="grid items-center gap-12 lg:grid-cols-2">
        <div className="hidden space-y-8 lg:block lg:pr-12">
          <div className="text-center lg:text-left">
            <motion.h1
              className="animate-gradient mb-4 bg-gradient-to-r from-[#4A90E2] via-[#50C878] to-[#F5A623] bg-clip-text text-4xl font-bold text-transparent md:text-5xl"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              Kết nối với Nhiếp ảnh gia Chuyên nghiệp
            </motion.h1>
            <motion.p
              className="text-muted-foreground text-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              Nền tảng kết nối hoàn hảo giữa khách hàng và nhiếp ảnh gia
            </motion.p>
          </div>

          <div className="grid gap-6 sm:grid-cols-2">
            {[
              {
                Icon: ImageDownIcon,
                title: 'Chất lượng Hình ảnh',
                desc: 'Portfolio chất lượng cao từ các nhiếp ảnh gia',
              },
              { Icon: Users, title: 'Kết nối Dễ dàng', desc: 'Đặt lịch và trao đổi trực tiếp với nhiếp ảnh gia' },
              { Icon: Star, title: 'Đánh giá Thực tế', desc: 'Phản hồi từ khách hàng thực tế' },
              { Icon: Award, title: 'Nhiếp ảnh gia Chọn lọc', desc: 'Đội ngũ nhiếp ảnh gia chuyên nghiệp' },
            ].map(({ Icon, title, desc }, index) => (
              <motion.div
                key={title}
                className="flex cursor-pointer items-start space-x-4 rounded-lg bg-[#F5F7FA]/50 p-4 backdrop-blur-sm"
                variants={cardVariants}
                initial="initial"
                whileHover="hover"
                transition={{ delay: index * 0.1 }}
              >
                <Icon className="mt-1 h-6 w-6 shrink-0 text-[#4A90E2]" />
                <div>
                  <h3 className="mb-1 font-semibold">{title}</h3>
                  <p className="text-muted-foreground text-sm">{desc}</p>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            <StatsSection />
          </motion.div>
        </div>

        {/* Right side - Auth Form */}
        <div className="relative min-w-[400px] sm:min-w-[520px]">
          <motion.div
            className="bg-background/80 relative rounded-2xl p-1 shadow-xl backdrop-blur-sm"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            whileHover={{ boxShadow: '0 0 20px rgba(74, 144, 226, 0.3)' }}
          >
            <AuthFlow />
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default AuthLandingPage

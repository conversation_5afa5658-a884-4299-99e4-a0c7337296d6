// // app/page.tsx
// import HeroBanner from '@/components/home/<USER>'
// import SearchBar from '@/components/home/<USER>'
// import { publicDataService } from '@/services/fetch-ssr.service'
// import Link from 'next/link'

import { redirect } from 'next/navigation'

// export default async function Home() {
//   const response = await publicDataService.getPublic()
//   console.log(response)

//   return (
//     <main className="container relative mx-auto">
//       <HeroBanner />
//       <SearchBar />
//       <h1>Welcome to Next.js 15 Auth</h1>
//       {/* <p>{response?.data?.message}</p> */}
//       <Link href="/sign-in">Login</Link> | <Link href="/protected">Protected Page</Link>
//     </main>
//   )
// }

export default function Page() {
  redirect('/shootings')
}

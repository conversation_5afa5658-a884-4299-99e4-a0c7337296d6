'use client'
import Bookings from '@/components/booking'
import { useUserStore } from '@/stores/auth.store'

const BookingsPage = () => {
  const { user } = useUserStore()

  if (!user || user.roleId === '2') {
    return null
  }

  return (
    <div className="min-h-screen bg-brand-gray_light pb-16">
      <section className="bg-gradient-to-r from-indigo-600 to-purple-600 py-20 text-white">
        <div className="container mx-auto flex flex-col items-center px-4 text-center md:px-6">
          <h1 className="mb-4 text-4xl font-extrabold tracking-tight md:text-5xl">Đặt Lịch Chụp Ảnh <PERSON><PERSON><PERSON></h1>
          <p className="mx-auto max-w-2xl text-lg leading-relaxed opacity-90 md:text-xl">
            Kết nối với nhiếp ảnh gia hoàn hảo cho buổi chụp ảnh của bạn bằng cách cung cấp thông tin chi tiết.
          </p>
        </div>
      </section>

      <div className="container mx-auto -mt-8 px-4">
        <div className="rounded-xl bg-white p-6 shadow-lg md:p-8">
          <Bookings />
        </div>
      </div>
    </div>
  )
}

export default BookingsPage

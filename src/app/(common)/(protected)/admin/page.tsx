'use client'

import PostTab from '@/components/admin/PostTab'
import UserTab from '@/components/admin/UserTab'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useUserStore } from '@/stores/auth.store'

const AdminPage = () => {
  const { user } = useUserStore()

  if (!user || user.roleId !== '3') {
    return null
  }

  return (
    <div className="space-y-6 p-6">
      <h1 className="mb-4 text-3xl font-semibold">Admin Dashboard</h1>
      <Tabs defaultValue="posts">
        <TabsList className="flex justify-start space-x-4">
          <TabsTrigger value="posts">Posts</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
        </TabsList>

        <TabsContent value="posts">
          <PostTab />
        </TabsContent>

        <TabsContent value="users">
          <UserTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AdminPage

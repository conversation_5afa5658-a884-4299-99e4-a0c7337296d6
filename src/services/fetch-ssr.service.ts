/* eslint-disable @typescript-eslint/no-explicit-any */
import { fetchPublicData } from '@/lib/ssr-api'
import { cache } from 'react'

export const publicDataService = {
  getPublic: cache(async () => {
    return fetchPublicData<any[]>('/auth/public')
  }),

  getPublicProducts: cache(async (category?: string, limit?: number) => {
    return fetchPublicData<any[]>('/products/public', { category, limit })
  }),

  getPublicArticles: cache(async (tag?: string, limit: number = 10) => {
    return fetchPublicData<any[]>('/articles/public', { tag, limit })
  }),

  getPublicProductDetails: cache(async (slug: string) => {
    return fetchPublicData<any>(`/products/public/${slug}`)
  }),
}

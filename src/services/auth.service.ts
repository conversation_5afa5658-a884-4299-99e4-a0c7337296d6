import picnowApi from '@/lib/csr-api'
import { LoginCredentials, LoginResponse, RegisterCredentials } from '@/types/auth.type'

export const authService = {
  register: async (credentials: RegisterCredentials) => {
    const response = await picnowApi.post('/auth/register', credentials, { withCredentials: true })
    return response.data.data
  },

  login: async (credentials: LoginCredentials) => {
    const response = await picnowApi.post('/auth/login', credentials, { withCredentials: true })
    return response.data.data
  },

  logout: async (logoutAll: boolean = false): Promise<void> => {
    await picnowApi.post('/auth/logout', { logoutAll }, { withCredentials: true })
  },

  refresh: async (): Promise<LoginResponse> => {
    const response = await picnowApi.post('/auth/refresh', {}, { withCredentials: true })
    return response.data
  },

  getProtectedData: async () => {
    const response = await picnowApi.post('/auth/protected')
    return response.data
  },
}

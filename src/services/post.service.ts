import { PhotographerRequestParams } from '@/hooks/use-post'
import picnowApi from '@/lib/csr-api'
import { PostDetail, PostSortQueryParams, PostStatus } from '@/types/post-schema.type'

export const postService = {
  createPost: async (post: Partial<PostDetail>) => {
    const response = await picnowApi.post<PostDetail>('/posts', post)
    return response
  },

  getPendingPosts: async () => {
    const response = await picnowApi.get<PostDetail[]>('/posts/pending')
    return response
  },

  updatePostStatus: async (postId: string, status: PostStatus, rejectedReason?: string) => {
    const response = await picnowApi.post(`/posts/${postId}/status`, { status, rejectedReason })
    return response.data
  },

  findSortedPosts: async (params?: PostSortQueryParams) => {
    const queryParams = new URLSearchParams()
    if (params?.search) {
      queryParams.append('search', params.search)
    }
    if (params?.page) {
      queryParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      queryParams.append('limit', params.limit.toString())
    }
    if (params?.sort && params?.direction) {
      queryParams.append('sort', `${params.sort}:${params.direction}`)
    }
    if (params?.status) {
      queryParams.append('status', params.status.toString())
    }
    if (params?.accountId) {
      queryParams.append('accountId', params.accountId.toString())
    }
    const queryString = queryParams.toString()
    const url = queryString ? `/posts?${queryString}` : '/posts'
    const response = await picnowApi.get(url)
    return response
  },

  getPostDetail: async (postId: string, params?: { [key: string]: string }) => {
    const queryParams = new URLSearchParams(params).toString()
    const url = queryParams ? `/posts/${postId}?${queryParams}` : `/posts/${postId}`
    const response = await picnowApi.get(url)
    return response.data?.data
  },

  sendPhotographerRequest: async ({ postId }: PhotographerRequestParams) => {
    const response = await picnowApi.post<PostDetail>('/posts/photographer-request', {
      postId,
    })
    return response
  },

  getPostsByPhotographer: async () => {
    const response = await picnowApi.get(`/posts/photographer/requests`)
    return response.data
  },

  choosePhotographer: async ({ postId, requestId }: { postId: string; requestId: string }) => {
    const response = await picnowApi.post<PostDetail>(`/posts/${postId}/choose-photographer`, {
      requestId,
    })
    return response.data
  },

  confirmBooking: async ({ bookingId }: { bookingId: string }) => {
    const response = await picnowApi.post(`/posts/booking/${bookingId}/confirm`)
    return response.data
  },
}

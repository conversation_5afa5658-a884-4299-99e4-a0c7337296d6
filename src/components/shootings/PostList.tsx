import {
  Sheet,
  She<PERSON><PERSON><PERSON>,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet'
import locationOptions from '@/constants/locations'
import { usePost } from '@/hooks/use-post'
import { useUserStore } from '@/stores/auth.store'
import { PostDetailViewType } from '@/types/post-schema.type'
import { format } from 'date-fns'
import { useState } from 'react'
import { Button } from '../ui/button'
import { Skeleton } from '../ui/skeleton'
import { PostDetailView } from './PostDetailView'

interface PostListProps {
  posts: PostDetailViewType[]
  isLoading: boolean
  isError?: boolean
  error?: Error | null
}

const SkeletonPostCard = () => (
  <div className="group block rounded-lg border border-brand-gray_medium bg-brand-white p-6 transition-all duration-200">
    <div className="space-y-3">
      <Skeleton className="h-6 w-3/4 bg-gray-200" />
      <Skeleton className="h-4 w-1/2 bg-gray-200" />
      <div className="grid grid-cols-1 gap-4 text-sm text-brand-text-secondary md:grid-cols-3">
        <div className="flex items-center">
          <Skeleton className="h-4 w-16 bg-gray-200" />
        </div>
        <div className="flex items-center">
          <Skeleton className="h-4 w-24 bg-gray-200" />
        </div>
        <div className="text-brand-success">
          <Skeleton className="h-4 w-32 bg-gray-200" />
        </div>
      </div>
    </div>
  </div>
)

const renderError = (error?: Error | null) => (
  <div className="text-center text-lg font-semibold text-red-600">
    <h2>Có lỗi xảy ra!</h2>
    <p className="text-sm text-gray-500">{error?.message || 'Không thể tải dữ liệu. Vui lòng thử lại sau.'}</p>
  </div>
)

const renderEmptyState = () => (
  <div className="text-center text-lg font-semibold text-gray-600">
    <h2>Không có bài viết nào.</h2>
    <p className="text-sm text-gray-500">Hiện tại không có bài viết nào. Vui lòng thử lại sau.</p>
  </div>
)

export default function PostList({ posts, isLoading, isError = false, error = null }: PostListProps) {
  const { user } = useUserStore()

  const [selectedPost, setSelectedPost] = useState<string | null>(null)
  const [isSheetOpen, setIsSheetOpen] = useState(false)

  const {
    data: postDetail,
    isLoading: isPostDetailLoading,
    error: postDetailError,
  } = usePost().usePostDetail(selectedPost || '', user?.id)

  const handlePostClick = (postId: string) => {
    setSelectedPost(postId)
    setIsSheetOpen(true)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <SkeletonPostCard key={index} />
          ))}
      </div>
    )
  }

  if (isError) {
    return renderError(error)
  }

  if (posts.length === 0) {
    return renderEmptyState()
  }

  return (
    <div className="space-y-6">
      {posts.map((post) => (
        <div
          key={post.post.id}
          onClick={() => post.post.id && handlePostClick(post.post.id)}
          className="group block cursor-pointer rounded-lg border border-brand-gray_medium bg-brand-white p-6 transition-all duration-200 hover:border-brand-primary hover:shadow-lg"
        >
          <div className="space-y-3">
            <h3 className="line-clamp-1 text-xl font-semibold text-brand-text-primary group-hover:text-brand-primary">
              {post.post.title}
            </h3>
            <div className="text-sm text-brand-accent">{post.post.category}</div>
            <div className="grid grid-cols-1 gap-4 text-sm text-brand-text-secondary md:grid-cols-3">
              <div className="flex items-center">
                <span className="mr-2">📅</span>
                <span>{format(new Date(post.post.shootingDate), 'dd/MM/yyyy')}</span>
              </div>
              <div className="flex items-center">
                <span className="mr-2">📍</span>
                <span className="line-clamp-1">
                  {locationOptions.find((opt) => opt.id === post.post.location)?.name || 'Chưa xác định'}
                </span>
              </div>
              <div className="text-brand-success">
                Ngân sách: {post.post.minBudget && `${post.post.minBudget.toLocaleString()}đ`}
                {post.post.minBudget && post.post.maxBudget && ' - '}
                {post.post.maxBudget && `${post.post.maxBudget.toLocaleString()}đ`}
              </div>
            </div>
            {/* {post.isBooked && post.bookingStatus && (
              <div className="mt-2 text-sm text-brand-success">
                <span>
                  Đã xác nhận với nhiếp ảnh gia:{' '}
                  {post.post.PhotographerRequest.find((req) => req.status === 'ACCEPTED')?.photographer.account.username}
                </span>
              </div>
            )} */}
          </div>
        </div>
      ))}

      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader>
            <SheetTitle>Chi tiết bài viết</SheetTitle>
            <SheetDescription>Xem thông tin chi tiết của bài viết đã chọn.</SheetDescription>
          </SheetHeader>
          <div className="py-6">
            {isPostDetailLoading ? (
              <SkeletonPostCard />
            ) : postDetailError ? (
              <div className="text-center text-lg font-semibold text-red-600">Không thể tải chi tiết bài viết.</div>
            ) : (
              postDetail && <PostDetailView postDetail={postDetail} />
            )}
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <Button variant="outline" className="w-full">
                Đóng
              </Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  )
}

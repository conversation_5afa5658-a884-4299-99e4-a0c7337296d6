'use client'

import FilterSidebar from '@/components/shootings/FilterSidebar'
import PostList from '@/components/shootings/PostList'
import { usePost } from '@/hooks/use-post'
import { PostDetail, PostStatus } from '@/types/post-schema.type'
import { useState } from 'react'

const Shootings = () => {
  const { useFindAllPosts } = usePost()

  const [filters, setFilters] = useState({
    search: '',
    categories: [] as string[],
    location: '',
  })

  const {
    data: AllData,
    isLoading,
    isError,
    error,
  } = useFindAllPosts({
    page: 1,
    limit: 10,
    sort: 'postDate',
    direction: 'desc',
    search: filters.search || undefined,
    status: PostStatus.APPROVED,
  })

  const handleFilterChange = (newFilters: { search: string; categories: string[]; location: string }) => {
    setFilters(newFilters)
  }

  const filteredPosts =
    AllData?.data?.data?.filter((post: PostDetail) => {
      const matchesSearch = !filters.search || post.title?.toLowerCase().includes(filters.search.toLowerCase())
      const matchesCategories = filters.categories.length === 0 || filters.categories.includes(post.category)
      const matchesLocation = !filters.location || post.location === filters.location

      return matchesSearch && matchesCategories && matchesLocation
    }) ?? []

  return (
    <main className="min-h-screen bg-brand-gray_light">
      <div className="container mx-auto py-8">
        <h1 className="mb-8 text-2xl font-bold text-brand-text-primary">Danh sách buổi chụp</h1>
        <div className="flex flex-col gap-6 md:flex-row">
          <aside className="w-full md:w-[360px]">
            <FilterSidebar onFilterChange={handleFilterChange} />
          </aside>
          <section className="w-full">
            <PostList posts={filteredPosts} isLoading={isLoading} isError={isError} error={error} />
          </section>
        </div>
      </div>
    </main>
  )
}

export default Shootings

'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { categoryOptions } from '@/constants/category-options'
import locationOptions from '@/constants/locations'
import { cn } from '@/lib/utils'
import { Check, ChevronsUpDown, Search, X } from 'lucide-react'
import { useState } from 'react'

interface FilterSidebarProps {
  onFilterChange: (filters: { search: string; categories: string[]; location: string }) => void
}

export default function FilterSidebar({ onFilterChange }: FilterSidebarProps) {
  const [search, setSearch] = useState('')
  const [categories, setCategories] = useState<string[]>([])
  const [location, setLocation] = useState('')
  const [openLocation, setOpenLocation] = useState(false)

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    if (checked) {
      setCategories((prev) => [...prev, categoryId])
    } else {
      setCategories((prev) => prev.filter((id) => id !== categoryId))
    }
  }

  const handleApplyFilters = () => {
    onFilterChange({
      search,
      categories,
      location,
    })
  }

  const handleResetFilters = () => {
    setSearch('')
    setCategories([])
    setLocation('')
    onFilterChange({
      search: '',
      categories: [],
      location: '',
    })
  }

  return (
    <div className="sticky top-20 rounded-lg border border-brand-gray_medium bg-brand-white p-4">
      <h2 className="mb-4 text-lg font-semibold text-brand-text-primary">Bộ lọc</h2>
      <div className="space-y-4">
        {/* Tìm kiếm */}
        <div>
          <Label htmlFor="search" className="mb-1 block text-sm text-brand-text-secondary">
            Tìm kiếm
          </Label>
          <div className="relative">
            <Input
              id="search"
              type="text"
              placeholder="Tìm kiếm bài viết..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full pl-10"
            />
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            {search && (
              <button
                onClick={() => setSearch('')}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>

        {/* Danh mục (Checkbox) */}
        <div>
          <Label className="mb-1 block text-sm text-brand-text-secondary">Danh mục</Label>
          <div className="space-y-2">
            {categoryOptions.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={category.id}
                  checked={categories.includes(category.id)}
                  onCheckedChange={(checked) => handleCategoryChange(category.id, checked as boolean)}
                />
                <Label htmlFor={category.id} className="text-sm text-brand-text-secondary">
                  {category.name}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Địa điểm (Select) */}
        <div>
          <Label htmlFor="location" className="mb-1 block text-sm text-brand-text-secondary">
            Địa điểm
          </Label>
          <Popover open={openLocation} onOpenChange={setOpenLocation}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                className={cn(
                  'w-full justify-between rounded-md border-brand-gray_medium p-2 text-sm text-brand-text-secondary hover:text-brand-text-secondary',
                  location && 'border-brand-primary/50',
                )}
              >
                {location ? locationOptions.find((option) => option.id === location)?.name : 'Chọn địa điểm'}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
              <Command className="w-full">
                <CommandInput placeholder="Tìm kiếm địa điểm..." />
                <CommandList className="max-h-[300px] w-full overflow-y-auto">
                  <CommandEmpty>Không tìm thấy địa điểm.</CommandEmpty>
                  <CommandGroup>
                    <CommandItem
                      value=""
                      onSelect={() => {
                        setLocation('')
                        setOpenLocation(false)
                      }}
                    >
                      <Check className={cn('mr-2 h-4 w-4', location === '' ? 'opacity-100' : 'opacity-0')} />
                      Tất cả
                    </CommandItem>
                    {locationOptions.map((option) => (
                      <CommandItem
                        key={option.id}
                        value={option.name}
                        onSelect={() => {
                          setLocation(option.id)
                          setOpenLocation(false)
                        }}
                      >
                        <Check className={cn('mr-2 h-4 w-4', location === option.id ? 'opacity-100' : 'opacity-0')} />
                        {option.name}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        {/* Nút áp dụng và xóa bộ lọc */}
        <div className="flex gap-2">
          <Button
            onClick={handleApplyFilters}
            className="w-full bg-brand-primary text-brand-white hover:bg-brand-accent"
          >
            Áp dụng bộ lọc
          </Button>
          <Button
            onClick={handleResetFilters}
            variant="outline"
            className="w-full border-brand-gray_medium text-brand-text-primary"
          >
            Xóa bộ lọc
          </Button>
        </div>
      </div>
    </div>
  )
}

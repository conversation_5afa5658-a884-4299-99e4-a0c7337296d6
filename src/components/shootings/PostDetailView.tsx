'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import locationOptions from '@/constants/locations'
import { usePost } from '@/hooks/use-post'
import { cn } from '@/lib/utils'
import { useUserStore } from '@/stores/auth.store'
import { PostDetailViewType } from '@/types/post-schema.type'
import { format } from 'date-fns'
import { Calendar, CheckCircle, DollarSign, MapPin, Tag, Users } from 'lucide-react' // Thêm CheckCircle
import { useRouter } from 'next/navigation'

interface PostDetailViewProps {
  postDetail: PostDetailViewType & {
    hasRequested?: boolean
    requestStatus?: string | null
    isBooked?: boolean
    bookingStatus?: string | null
  }
}

export const PostDetailView = ({ postDetail }: PostDetailViewProps) => {
  const { user } = useUserStore()
  const router = useRouter()
  const { sendRequest, isRequestPending, isRequestSuccess, useFindAllPosts } = usePost()
  const { refetch } = useFindAllPosts()

  const { post, totalPhotographerRequests, hasRequested, isBooked, bookingStatus } = postDetail

  const handlePhotographerRequest = () => {
    if (!user || user.roleId !== '2' || !post.id) return
    sendRequest({ postId: post.id })
    refetch()
  }

  const getDisplayStatus = () => {
    if (bookingStatus) {
      switch (bookingStatus) {
        case 'PENDING':
          return 'Chờ thanh toán'
        case 'CONFIRMED':
          return 'Đã xác nhận đặt lịch'
        case 'COMPLETED':
          return 'Đã hoàn thành'
        case 'CANCELLED':
          return 'Đã hủy'
        default:
          return null
      }
    }
    return null
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Chờ duyệt'
      case 'APPROVED':
        return 'Đang tìm nhiếp ảnh gia'
      case 'REJECTED':
        return 'Bị từ chối'
      case 'EXPIRED':
        return 'Hết hạn'
      case 'COMPLETED':
        return 'Đã hoàn thành'
      default:
        return status
    }
  }

  const renderActionSection = () => {
    if (!user) {
      return (
        <Button
          variant="default"
          onClick={() => (window.location.href = `/sign-in`)}
          className="w-full rounded-lg bg-gradient-to-r from-orange-500 to-orange-600 py-3 font-semibold text-white shadow-md hover:from-orange-600 hover:to-orange-700"
        >
          Đăng nhập để tham gia
        </Button>
      )
    }

    if (post.status === 'EXPIRED' || post.status === 'COMPLETED') {
      return (
        <p className="text-center text-sm text-gray-500">Bài đăng đã {getStatusLabel(post.status).toLowerCase()}.</p>
      )
    }

    switch (user.roleId) {
      case '1': // Customer
        return user.id === post.accountId ? (
          <div className="space-y-2">
            <p className="text-sm text-gray-500">Có {totalPhotographerRequests} nhiếp ảnh gia đã gửi đề nghị</p>
            {(totalPhotographerRequests ?? 0) > 0 && (
              <Button
                variant="default"
                onClick={() => router.push(`/customer`)}
                className="w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 py-3 font-semibold text-white shadow-md hover:from-blue-600 hover:to-blue-700"
              >
                <Users className="mr-2 h-4 w-4" />
                Quản lý bài đăng
              </Button>
            )}
          </div>
        ) : (
          <p className="text-center text-sm text-gray-500">Chỉ chủ bài đăng mới có thể xem yêu cầu.</p>
        )
      case '2': // Photographer
        return (
          <Button
            variant="default"
            onClick={handlePhotographerRequest}
            disabled={
              isRequestPending ||
              isRequestSuccess ||
              hasRequested ||
              user.id === post.accountId ||
              post.status !== 'APPROVED' ||
              (isBooked && bookingStatus !== 'CANCELLED')
            }
            className={cn(
              'w-full rounded-lg py-3 font-semibold text-white shadow-md transition-all',
              hasRequested || isRequestSuccess
                ? 'bg-green-500 hover:bg-green-600'
                : 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',
            )}
          >
            {isRequestPending
              ? 'Đang gửi...'
              : hasRequested || isRequestSuccess
                ? `Đã gửi đề nghị`
                : 'Gửi đề nghị chụp ảnh'}
          </Button>
        )
      case '3': // Admin
        return <p className="text-center text-sm text-gray-500">Admin không thể gửi đề nghị.</p>
      default:
        return null
    }
  }

  return (
    <div className="relative mx-auto max-w-2xl rounded-xl bg-white pt-6">
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{post.title}</h2>
          <p className="text-xs text-gray-400">Mã: #{post.id}</p>
        </div>
        <div className="absolute right-0 top-0 flex flex-col gap-2">
          {isBooked && getDisplayStatus() && (
            <Badge
              variant="outline"
              className={cn('text-sm font-medium', {
                'border-yellow-500 text-yellow-500': bookingStatus === 'PENDING',
                'border-blue-500 text-blue-500': bookingStatus === 'CONFIRMED',
                'border-green-500 text-green-500': bookingStatus === 'COMPLETED',
                'border-red-500 text-red-500': bookingStatus === 'CANCELLED',
              })}
            >
              <CheckCircle className="mr-1 h-4 w-4" />
              {getDisplayStatus()}
            </Badge>
          )}
        </div>
      </div>

      {/* Thông tin chi tiết */}
      <div className="space-y-4 text-gray-700">
        <div className="flex items-center gap-2">
          <Tag className="h-4 w-4" />
          <span>{post.category}</span>
        </div>
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          <span>{locationOptions.find((opt) => opt.id === post.location)?.name || 'Chưa xác định'}</span>
        </div>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <span>{post.shootingDate ? format(new Date(post.shootingDate), 'dd/MM/yyyy (EEEE)') : 'Chưa xác định'}</span>
        </div>
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4" />
          <span className="font-bold text-gray-900">
            {post.minBudget?.toLocaleString('vi-VN')} - {post.maxBudget?.toLocaleString('vi-VN')} VND
          </span>
        </div>
      </div>

      {post.additionalServices && (
        <div className="mt-6">
          <h3 className="mb-2 text-sm font-semibold text-gray-800">Yêu cầu bổ sung</h3>
          <div className="flex flex-wrap gap-2">
            {post.additionalServices.map((service, index) => (
              <Badge key={index} variant="secondary" className="bg-gray-100 text-gray-700">
                {service.label}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <div className="mt-6">{renderActionSection()}</div>
    </div>
  )
}

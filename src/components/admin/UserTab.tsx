'use client'
import { useEffect, useState } from 'react'
import { Button } from '../ui/button'

const UserTab = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [users, setUsers] = useState<any[]>([])

  useEffect(() => {
    // Fetch users from API (replace with your API call)
    const fetchedUsers = [
      { id: '1', name: 'User 1' },
      { id: '2', name: 'User 2' },
    ]
    setUsers(fetchedUsers)
  }, [])

  const handleDeactivate = (userId: string) => {
    // Call API to deactivate user
    console.log(`Deactivating user with ID: ${userId}`)
  }

  return (
    <div className="space-y-4">
      {users.map((user) => (
        <div key={user.id} className="flex items-center justify-between rounded-lg border p-4">
          <div className="flex-1">
            <h3 className="font-semibold">{user.name}</h3>
          </div>
          <div className="space-x-2">
            <Button onClick={() => handleDeactivate(user.id)} variant="outline" color="red">
              Deactivate
            </Button>
          </div>
        </div>
      ))}
    </div>
  )
}

export default UserTab

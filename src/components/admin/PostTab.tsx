'use client'

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON>nt,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { usePost } from '@/hooks/use-post'
import { PostStatus } from '@/types/post-schema.type'
import { useState } from 'react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'

const PostTab = () => {
  const { pendingPosts, updatePostStatus } = usePost()

  const [rejectReason, setRejectReason] = useState<string>('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentPostId, setCurrentPostId] = useState<string | null>(null)

  const handleApprove = (postId: string) => {
    updatePostStatus({ postId, status: PostStatus.APPROVED })
  }

  const handleReject = (postId: string) => {
    setCurrentPostId(postId)
    setIsDialogOpen(true)
  }

  const handleRejectSubmit = () => {
    if (rejectReason && currentPostId) {
      updatePostStatus({ postId: currentPostId, status: PostStatus.REJECTED, rejectedReason: rejectReason })
      setIsDialogOpen(false)
      setRejectReason('')
    } else {
      alert('Please provide a reason for rejection.')
    }
  }

  const handleDialogClose = () => {
    setIsDialogOpen(false)
    setRejectReason('')
  }

  console.log(pendingPosts)

  return (
    <div className="space-y-4">
      {pendingPosts?.data?.map((post) => (
        <div key={post.id} className="flex items-center justify-between rounded-lg border p-4">
          <div className="flex-1">
            <h3 className="font-semibold">{post.title}</h3>
            <span className="text-sm text-gray-500">{post.status}</span>
          </div>
          <div className="space-x-2">
            <Button onClick={() => post.id && handleApprove(post.id)} variant="outline" color="green">
              Approve
            </Button>
            <Button onClick={() => post.id && handleReject(post.id)} variant="outline" color="red">
              Reject
            </Button>
          </div>
        </div>
      ))}

      {/* AlertDialog for Reject Reason */}
      <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <AlertDialogTrigger />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <p>Reject Post</p>
            </AlertDialogTitle>
          </AlertDialogHeader>
          <Input
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            placeholder="Enter the reason for rejection"
            className="w-full rounded-md border p-2"
          />
          <AlertDialogFooter>
            <Button variant="outline" color="gray" onClick={handleDialogClose}>
              Cancel
            </Button>
            <Button variant="outline" color="red" onClick={handleRejectSubmit}>
              Submit
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default PostTab

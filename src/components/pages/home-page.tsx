import {
  HeroSection,
  PopularCategoriesSection,
  FeaturedJobsSection,
  HowItWorksSection,
  PopularCompaniesSection,
  StatisticsSection,
  RecentNewsSection,
  MobileAppSection,
  NewsletterSection,
} from "@/components/sections";

// Mock data for job listings
const mockJobs = [
  {
    id: "1",
    title: "Software Engineer (Android), Libraries",
    description:
      "We are looking for an experienced Android developer to join our team.",
    summary:
      "Join our team to create incredible Android experiences for millions of users.",
    requirements: [
      "5+ years of Android development",
      "Kotlin experience",
      "Experience with modern architecture patterns",
    ],
    benefits: ["Competitive salary", "Health insurance", "Flexible work hours"],
    location: "London, UK",
    workType: "remote" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 35000,
      max: 45000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c1",
      name: "Segment",
      logo: "/company-logos/segment.svg",
      description: "Leading analytics company",
      website: "https://segment.com",
      size: "501-1000",
      industry: "Technology",
      location: "London, UK",
      rating: 4.8,
    },
    skills: ["Android", "Kotlin", "Java", "RxJava", "MVVM"],
    tags: ["Android", "Mobile", "Software Development"],
    postedAt: "2023-07-20T10:00:00Z",
    deadline: "2023-08-20T10:00:00Z",
    applicationsCount: 25,
    isBookmarked: true,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "2",
    title: "Frontend Developer",
    description:
      "Frontend developer with React experience needed for a fast growing startup.",
    summary:
      "Join our team to build modern web applications using React and Next.js.",
    requirements: [
      "3+ years of React development",
      "TypeScript knowledge",
      "Experience with modern frontend frameworks",
    ],
    benefits: ["Competitive salary", "Remote work", "Learning budget"],
    location: "Berlin, Germany",
    workType: "hybrid" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 50000,
      max: 65000,
      currency: "€",
      period: "yearly" as const,
    },
    company: {
      id: "c2",
      name: "TechStart",
      logo: "/company-logos/techstart.svg",
      description: "Innovative tech startup",
      website: "https://techstart.io",
      size: "11-50",
      industry: "Technology",
      location: "Berlin, Germany",
      rating: 4.5,
    },
    skills: ["React", "TypeScript", "Next.js", "CSS", "HTML"],
    tags: ["Frontend", "React", "Web Development"],
    postedAt: "2023-07-18T14:30:00Z",
    deadline: "2023-08-18T14:30:00Z",
    applicationsCount: 42,
    isBookmarked: false,
    featured: false,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "3",
    title: "UX/UI Designer",
    description:
      "We're looking for a UX/UI Designer to create amazing user experiences.",
    summary:
      "Design beautiful and intuitive interfaces for web and mobile applications.",
    requirements: [
      "3+ years of UX/UI design experience",
      "Proficiency with Figma or similar tools",
      "Portfolio showcasing previous work",
    ],
    benefits: [
      "Competitive salary",
      "Flexible schedule",
      "Creative environment",
    ],
    location: "New York, USA",
    workType: "onsite" as const,
    employmentType: "contract" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 80000,
      max: 100000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c3",
      name: "DesignHub",
      logo: "/company-logos/designhub.svg",
      description: "Creative design agency",
      website: "https://designhub.co",
      size: "51-200",
      industry: "Design",
      location: "New York, USA",
      rating: 4.7,
    },
    skills: ["UI Design", "UX Design", "Figma", "Prototyping", "User Research"],
    tags: ["Design", "UX", "UI"],
    postedAt: "2023-07-15T09:15:00Z",
    deadline: "2023-08-15T09:15:00Z",
    applicationsCount: 18,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "4",
    title: "Backend Developer (Node.js)",
    description:
      "Experienced backend developer needed for scalable applications.",
    summary:
      "Build robust APIs and microservices using Node.js and cloud technologies.",
    requirements: [
      "4+ years of Node.js development",
      "Experience with databases (MongoDB, PostgreSQL)",
      "Knowledge of cloud platforms (AWS, GCP)",
    ],
    benefits: ["High salary", "Stock options", "Health benefits"],
    location: "San Francisco, USA",
    workType: "remote" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 80000,
      max: 120000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c4",
      name: "CloudTech",
      logo: "/company-logos/cloudtech.svg",
      description: "Cloud solutions provider",
      website: "https://cloudtech.com",
      size: "201-500",
      industry: "Technology",
      location: "San Francisco, USA",
      rating: 4.7,
    },
    skills: ["Node.js", "Express", "MongoDB", "AWS", "Docker"],
    tags: ["Backend", "Node.js", "Cloud"],
    postedAt: "2023-07-17T09:00:00Z",
    deadline: "2023-08-17T09:00:00Z",
    applicationsCount: 38,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "5",
    title: "DevOps Engineer",
    description:
      "DevOps engineer to manage infrastructure and deployment pipelines.",
    summary: "Automate deployments and manage cloud infrastructure at scale.",
    requirements: [
      "3+ years of DevOps experience",
      "Kubernetes and Docker expertise",
      "CI/CD pipeline experience",
    ],
    benefits: ["Competitive package", "Remote work", "Training budget"],
    location: "Toronto, Canada",
    workType: "hybrid" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 70000,
      max: 90000,
      currency: "CAD",
      period: "yearly" as const,
    },
    company: {
      id: "c5",
      name: "DevOps Pro",
      logo: "/company-logos/devops-pro.svg",
      description: "Infrastructure automation company",
      website: "https://devopspro.ca",
      size: "51-100",
      industry: "Technology",
      location: "Toronto, Canada",
      rating: 4.4,
    },
    skills: ["Kubernetes", "Docker", "AWS", "Terraform", "Jenkins"],
    tags: ["DevOps", "Infrastructure", "Automation"],
    postedAt: "2023-07-16T11:30:00Z",
    deadline: "2023-08-16T11:30:00Z",
    applicationsCount: 29,
    isBookmarked: true,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "6",
    title: "Data Scientist",
    description:
      "Data scientist to analyze large datasets and build ML models.",
    summary:
      "Extract insights from data and develop machine learning solutions.",
    requirements: [
      "PhD or Masters in Data Science/Statistics",
      "Python and R proficiency",
      "Machine learning experience",
    ],
    benefits: [
      "Excellent salary",
      "Research opportunities",
      "Conference budget",
    ],
    location: "London, UK",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 60000,
      max: 85000,
      currency: "£",
      period: "yearly" as const,
    },
    company: {
      id: "c6",
      name: "DataInsights",
      logo: "/company-logos/datainsights.svg",
      description: "Data analytics and AI company",
      website: "https://datainsights.co.uk",
      size: "101-200",
      industry: "Data & Analytics",
      location: "London, UK",
      rating: 4.6,
    },
    skills: ["Python", "R", "TensorFlow", "SQL", "Statistics"],
    tags: ["Data Science", "Machine Learning", "Analytics"],
    postedAt: "2023-07-15T13:45:00Z",
    deadline: "2023-08-15T13:45:00Z",
    applicationsCount: 51,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "7",
    title: "Product Manager",
    description: "Product manager to lead product strategy and development.",
    summary: "Drive product vision and work with cross-functional teams.",
    requirements: [
      "5+ years of product management experience",
      "Strong analytical skills",
      "Experience with agile methodologies",
    ],
    benefits: ["Leadership role", "Equity package", "Flexible hours"],
    location: "Seattle, USA",
    workType: "hybrid" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 100000,
      max: 140000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c7",
      name: "ProductCorp",
      logo: "/company-logos/productcorp.svg",
      description: "Product development company",
      website: "https://productcorp.com",
      size: "501-1000",
      industry: "Technology",
      location: "Seattle, USA",
      rating: 4.3,
    },
    skills: ["Product Strategy", "Analytics", "Agile", "Leadership", "SQL"],
    tags: ["Product Management", "Strategy", "Leadership"],
    postedAt: "2023-07-14T16:20:00Z",
    deadline: "2023-08-14T16:20:00Z",
    applicationsCount: 33,
    isBookmarked: true,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "8",
    title: "Mobile Developer (iOS)",
    description: "iOS developer to build native mobile applications.",
    summary: "Create amazing iOS apps using Swift and modern iOS frameworks.",
    requirements: [
      "4+ years of iOS development",
      "Swift and Objective-C proficiency",
      "App Store publishing experience",
    ],
    benefits: ["Competitive salary", "Device allowance", "Learning budget"],
    location: "Austin, USA",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 75000,
      max: 95000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c8",
      name: "MobileFirst",
      logo: "/company-logos/mobilefirst.svg",
      description: "Mobile app development studio",
      website: "https://mobilefirst.com",
      size: "11-50",
      industry: "Technology",
      location: "Austin, USA",
      rating: 4.5,
    },
    skills: ["Swift", "iOS", "Xcode", "Core Data", "UIKit"],
    tags: ["iOS", "Mobile", "Swift"],
    postedAt: "2023-07-13T12:10:00Z",
    deadline: "2023-08-13T12:10:00Z",
    applicationsCount: 27,
    isBookmarked: false,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "9",
    title: "QA Engineer",
    description: "Quality assurance engineer to ensure software quality.",
    summary: "Test applications and ensure high-quality software delivery.",
    requirements: [
      "3+ years of QA experience",
      "Automation testing skills",
      "Knowledge of testing frameworks",
    ],
    benefits: ["Good work-life balance", "Remote options", "Career growth"],
    location: "Amsterdam, Netherlands",
    workType: "remote" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 45000,
      max: 60000,
      currency: "€",
      period: "yearly" as const,
    },
    company: {
      id: "c9",
      name: "QualityTech",
      logo: "/company-logos/qualitytech.svg",
      description: "Software quality assurance company",
      website: "https://qualitytech.nl",
      size: "101-200",
      industry: "Technology",
      location: "Amsterdam, Netherlands",
      rating: 4.2,
    },
    skills: ["Selenium", "Jest", "Cypress", "API Testing", "Automation"],
    tags: ["QA", "Testing", "Automation"],
    postedAt: "2023-07-12T08:30:00Z",
    deadline: "2023-08-12T08:30:00Z",
    applicationsCount: 19,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
];

export default function HomePage() {
  return (
    <main className="min-h-screen">
      <HeroSection />
      <PopularCategoriesSection />
      <FeaturedJobsSection jobs={mockJobs} />
      <HowItWorksSection />
      <PopularCompaniesSection />
      <StatisticsSection />
      <RecentNewsSection />
      <MobileAppSection />
      <NewsletterSection />
    </main>
  );
}

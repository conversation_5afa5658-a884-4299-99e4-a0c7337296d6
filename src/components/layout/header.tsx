'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuth } from '@/hooks/use-auth'
import { cn } from '@/lib/utils'
import { useUserStore } from '@/stores/auth.store'
import { LogOut, Menu, User, X } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

const navLinks = [
  { href: '/about', label: 'Về Pi<PERSON>now' },
  { href: '/shootings', label: 'Danh Sách Buổi Chụp' },
  { href: '/bookings', label: 'Đặt Lịch' },
  { href: '/blogs', label: 'Blogs' },
]
const rolePaths: { [key: string]: string } = {
  '1': '/customer',
  '2': 'photographer',
  '3': '/admin',
}
export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const router = useRouter()
  const { logout } = useAuth()
  const { user } = useUserStore()

  const toggleMenu = () => setIsMenuOpen((prev) => !prev)
  const handleSignIn = () => router.push('/sign-in')
  const handleLogout = () => {
    logout()
  }

  const filteredNavLinks = ['2', '3'].includes(user?.roleId ?? '')
    ? navLinks.filter((link) => link.href !== '/bookings')
    : navLinks

  return (
    <header className="sticky top-0 z-50 w-full border-b border-brand-gray_medium bg-brand-white shadow-sm">
      <div className="container mx-auto flex items-center justify-between px-4 py-3">
        {/* Logo */}
        <Link href="/" className="flex items-center">
          <div className="relative mr-2 h-8 w-8">
            <div className="absolute inset-0 flex items-center justify-center rounded-md bg-gradient-to-br from-brand-primary to-brand-accent">
              <div className="h-4 w-4 rounded-full bg-brand-white"></div>
            </div>
          </div>
          <span className="text-xl font-bold text-brand-text-primary">Picnow</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden items-center space-x-6 text-sm font-semibold md:flex">
          {filteredNavLinks.map(({ href, label }) => (
            <Link
              key={href}
              href={href}
              className="font-medium text-brand-text-secondary transition-colors hover:text-brand-primary"
            >
              {label}
            </Link>
          ))}
        </nav>

        {/* Auth Section */}
        <div className="hidden items-center gap-4 md:flex">
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full p-0">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user.avatar} alt={user.username} />
                    <AvatarFallback className="bg-brand-gray_light text-brand-text-primary">
                      {user.username?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user.username || 'Người dùng'}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={rolePaths[user.roleId] || '/'} className="flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    <span>Quản lý</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="flex items-center">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Đăng xuất</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              onClick={handleSignIn}
              className="rounded-full bg-brand-primary px-6 text-white hover:bg-brand-primary/90"
            >
              Đăng nhập/Đăng ký
            </Button>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button className="text-brand-text-primary md:hidden" onClick={toggleMenu} aria-label="Toggle menu">
          {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* Mobile Navigation */}
      <div
        className={cn(
          'absolute left-0 w-full border-t border-brand-gray_medium bg-white shadow-md transition-all duration-300 ease-in-out md:hidden',
          isMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 overflow-hidden opacity-0',
        )}
      >
        <div className="container mx-auto space-y-4 px-4 py-3 text-sm font-semibold">
          {filteredNavLinks.map(({ href, label }) => (
            <Link
              key={href}
              href={href}
              className="block py-2 font-medium text-brand-text-secondary hover:text-brand-primary"
            >
              {label}
            </Link>
          ))}
          {user ? (
            <div className="space-y-4">
              <div className="flex items-center gap-3 border-b pb-4">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user.avatar} alt={user.username} />
                  <AvatarFallback className="bg-brand-gray_light text-brand-text-primary">
                    {user.username?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-brand-text-primary">{user.username || 'Người dùng'}</p>
                  <p className="text-xs text-brand-text-secondary">{user.email}</p>
                </div>
              </div>
              <Button
                onClick={handleLogout}
                className="w-full rounded-full bg-brand-primary text-white hover:bg-brand-primary/90"
              >
                Đăng xuất
              </Button>
            </div>
          ) : (
            <Button
              onClick={handleSignIn}
              className="w-full rounded-full bg-brand-primary text-white hover:bg-brand-primary/90"
            >
              Đăng nhập/Đăng ký
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}

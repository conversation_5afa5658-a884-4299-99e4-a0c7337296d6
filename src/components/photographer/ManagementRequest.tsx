/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { usePost } from '@/hooks/use-post'
import { AlertCircle, Loader2 } from 'lucide-react'
import { useState } from 'react'
import { RequestTable } from './RequestTable'

// Loading Component
const LoadingState = () => (
  <div className="flex h-screen items-center justify-center">
    <Loader2 className="h-8 w-8 animate-spin" />
  </div>
)

// Error Component
const ErrorState = ({ message }: { message: string }) => (
  <div className="flex h-screen flex-col items-center justify-center text-red-500">
    <AlertCircle className="mb-2 h-8 w-8" />
    <p>Error: {message}</p>
  </div>
)

// Empty State Component
const EmptyState = ({ message }: { message: string }) => (
  <div className="py-10 text-center">
    <p>{message}</p>
  </div>
)

// Main Component
const ManagementRequest = () => {
  const { usePostsByPhotographer, confirmBooking, confirmLoading, confirmSuccess } = usePost()
  const [confirmingId, setConfirmingId] = useState<string | null>(null)

  const { data: requests = [], isLoading, isError, error, refetch } = usePostsByPhotographer()

  const handleConfirm = (requestId: string) => {
    setConfirmingId(requestId)
    confirmBooking({ bookingId: requestId })
    refetch()
  }

  if (isLoading) return <LoadingState />
  if (isError) return <ErrorState message={error?.message || 'Something went wrong'} />
  if (!requests.length) return <EmptyState message="No requests found" />

  const pendingRequests = requests.filter((req: any) => req.status === 'PENDING')
  const acceptedRequests = requests.filter((req: any) => req.status === 'ACCEPTED')
  const rejectedRequests = requests.filter((req: any) => req.status === 'REJECTED')

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-6 text-2xl font-bold">Quản lý yêu cầu đã gửi</h1>
      <Tabs defaultValue="pending" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending">Chờ phản hồi ({pendingRequests.length})</TabsTrigger>
          <TabsTrigger value="accepted">KH Đã chấp nhận ({acceptedRequests.length})</TabsTrigger>
          <TabsTrigger value="rejected">KH Đã từ chối ({rejectedRequests.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          {pendingRequests.length ? (
            <RequestTable
              requests={pendingRequests}
              onConfirm={handleConfirm}
              confirmingId={confirmingId}
              confirmLoading={confirmLoading}
            />
          ) : (
            <EmptyState message="Không có yêu cầu đang chờ" />
          )}
        </TabsContent>

        <TabsContent value="accepted">
          {acceptedRequests.length ? (
            <RequestTable
              requests={acceptedRequests}
              onConfirm={handleConfirm}
              confirmingId={confirmingId}
              confirmSuccess={confirmSuccess}
              status="accepted"
            />
          ) : (
            <EmptyState message="Không có yêu cầu đã chấp nhận" />
          )}
        </TabsContent>

        <TabsContent value="rejected">
          {rejectedRequests.length ? (
            <RequestTable requests={rejectedRequests} onConfirm={handleConfirm} confirmingId={confirmingId} />
          ) : (
            <EmptyState message="Không có yêu cầu đã từ chối" />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ManagementRequest

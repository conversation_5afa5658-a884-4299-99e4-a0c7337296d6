'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { format } from 'date-fns'
import { CheckCircle2, Loader2 } from 'lucide-react'

export const RequestTable = ({
  requests,
  onConfirm,
  confirmingId,
  confirmLoading,
  confirmSuccess,
  status,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  requests: any[]
  onConfirm: (id: string) => void
  confirmingId: string | null
  confirmLoading?: boolean
  confirmSuccess?: boolean
  status?: string
}) => {
  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'ACCEPTED':
        return 'default'
      case 'REJECTED':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const renderActions = (requestId: string, bookingStatus: string) => {
    if (bookingStatus === 'CONFIRMED') {
      return (
        <Button variant={'secondary'} className="flex items-center text-green-600">
          <CheckCircle2 className="mr-2 h-4 w-4" />
          Đã xác nhận
        </Button>
      )
    } else {
      return (
        <Button
          size="sm"
          onClick={() => onConfirm(requestId)}
          disabled={confirmingId === requestId}
          variant={confirmingId === requestId && confirmSuccess ? 'secondary' : 'default'}
        >
          {confirmLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Đang xác nhận
            </>
          ) : confirmingId === requestId && confirmSuccess ? (
            <div className="flex items-center text-green-600">
              <CheckCircle2 className="mr-2 h-4 w-4" />
              Đã xác nhận
            </div>
          ) : (
            'Xác nhận'
          )}
        </Button>
      )
    }
  }

  console.log(requests)

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Tiêu đề</TableHead>
          <TableHead>Ngày gửi</TableHead>
          <TableHead>Ngày chụp</TableHead>
          <TableHead>Ngân sách</TableHead>
          <TableHead>Địa điểm</TableHead>
          <TableHead>Khách hàng</TableHead>
          <TableHead>Trạng thái</TableHead>
          <TableHead>Hành động</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {requests.map((request) => (
          <TableRow key={request.id}>
            <TableCell>{request.post.title}</TableCell>
            <TableCell>{format(new Date(request.requestDate), 'dd/MM/yyyy HH:mm')}</TableCell>
            <TableCell>{format(new Date(request.post.shootingDate), 'dd/MM/yyyy HH:mm')}</TableCell>
            <TableCell>
              {request.post.minBudget.toLocaleString()} - {request.post.maxBudget.toLocaleString()} VNĐ
            </TableCell>
            <TableCell>{`${request.post.location} - ${request.post.detailedLocation}`}</TableCell>
            <TableCell>
              {request.post.account.username} ({request.post.account.phoneNumber})
            </TableCell>
            <TableCell>
              <Badge variant={getBadgeVariant(request.status)}>{request.status}</Badge>
            </TableCell>
            {status === 'accepted' && (
              <TableCell>{renderActions(request.Booking[0]?.id, request.Booking[0]?.status)}</TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

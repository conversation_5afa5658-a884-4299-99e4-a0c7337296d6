import { cn } from '@/lib/utils'
import Image from 'next/image'

export interface IImageProps {
  size?: number
  className?: string
  src: string
}

export const LoadingSpinner = ({ size = 24, className, src, ...props }: IImageProps) => {
  return (
    <div className={cn(className)} {...props}>
      <Image src={src} alt="Loading" width={size} height={size} className="object-contain" priority />
    </div>
  )
}

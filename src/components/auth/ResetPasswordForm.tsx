import { Button } from '@/components/ui/button'
import { CardContent } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { UserType } from '@/types/auth'
import { zodResolver } from '@hookform/resolvers/zod'
import { Undo2 } from 'lucide-react'
import React from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

// Validation schema for reset password
const resetPasswordSchema = z
  .object({
    password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Mật khẩu xác nhận không khớp',
    path: ['confirmPassword'],
  })

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>

interface ResetPasswordFormProps {
  userType: UserType | null
  onBack: () => void
  onSwitchToLogin: () => void
  token?: string // Assuming a token from URL for reset validation
}

export const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ onBack, onSwitchToLogin, token }) => {
  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = (data: ResetPasswordFormValues) => {
    console.log('Reset password submitted:', { ...data, token })
    // Handle password reset submission here (e.g., API call with token)
  }

  return (
    <div className="mx-auto w-full max-w-md">
      <Button
        variant="ghost"
        className="absolute left-0 top-0 flex items-center space-x-2 rounded-lg px-3 py-2 text-[#4A90E2] transition-all duration-300 hover:bg-[#4A90E2]/10 hover:text-[#3A80D2]"
        onClick={onBack}
      >
        <Undo2 size={20} />
        <span className="text-sm font-semibold">Quay lại</span>
      </Button>

      <CardContent className="mt-6 space-y-3">
        <div className="space-y-1 text-center">
          <h2 className="bg-gradient-to-r from-[#4A90E2] to-[#6BA8E8] bg-clip-text text-2xl font-bold text-transparent">
            Đặt lại mật khẩu
          </h2>
          <p className="text-sm text-slate-600">Nhập mật khẩu mới của bạn</p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel className="font-semibold text-[#4A90E2]">Mật khẩu mới</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="password"
                      placeholder="Mật khẩu mới"
                      className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel className="font-semibold text-[#4A90E2]">Xác nhận mật khẩu</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="password"
                      placeholder="Xác nhận mật khẩu"
                      className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full rounded-xl bg-gradient-to-r from-[#4A90E2] to-[#50C878] py-5 text-lg font-semibold text-white shadow-md transition-all duration-300 hover:from-[#3A80D2] hover:to-[#40B868] hover:shadow-lg"
            >
              Đặt lại mật khẩu
            </Button>

            <div className="text-center">
              <Button variant="link" className="text-base text-[#4A90E2] hover:text-[#3A80D2]" onClick={onSwitchToLogin}>
                Quay lại <span className="font-semibold">Đăng nhập</span>
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </div>
  )
}

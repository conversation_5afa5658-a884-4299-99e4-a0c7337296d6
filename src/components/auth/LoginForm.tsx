import { Button } from '@/components/ui/button'
import { CardContent } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/hooks/use-auth'
import { UserType } from '@/types/auth'
import { zodResolver } from '@hookform/resolvers/zod'
import { Undo2 } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

const loginSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
})

type LoginFormValues = z.infer<typeof loginSchema>

interface LoginFormProps {
  userType: UserType | null
  onBack: () => void
  onSwitchToRegister: () => void
  onClose?: () => void
  onForgotPassword?: () => void
}

export const LoginForm: React.FC<LoginFormProps> = ({ onBack, onSwitchToRegister, onForgotPassword }) => {
  const { login, isLoginPending, isLoginError, errorMessageLogin } = useAuth()
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: 'viethoang',
    },
  })

  const onSubmit = (data: LoginFormValues) => {
    console.log('Login submitted:', data)
    login(data)
  }

  return (
    <div className="mx-auto w-full max-w-md">
      <Button
        variant="ghost"
        className="absolute left-0 top-0 flex items-center space-x-2 rounded-lg px-3 py-2 text-[#4A90E2] transition-all duration-300 hover:bg-[#4A90E2]/10 hover:text-[#3A80D2]"
        onClick={onBack}
      >
        <Undo2 size={20} />
        <span className="text-sm font-semibold">Quay lại</span>
      </Button>

      <CardContent className="mt-6 space-y-3">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <Button
              variant="outline"
              className="flex w-full items-center justify-center space-x-3 rounded-xl border-[#4A90E2] bg-[#F5F7FA]/50 py-5 text-[#4A90E2] transition-all duration-300 hover:border-[#3A80D2] hover:bg-[#4A90E2]/10"
            >
              <Image
                src="https://www.svgrepo.com/show/475656/google-color.svg"
                alt="Google"
                width={24}
                height={24}
                className="h-6 w-6"
              />
              <span className="text-lg font-semibold">Đăng nhập với Google</span>
            </Button>

            <div className="flex items-center text-sm text-slate-600">
              <div className="h-px w-full bg-slate-200"></div>
              <span className="px-3">HOẶC</span>
              <div className="h-px w-full bg-slate-200"></div>
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel className="font-semibold text-[#4A90E2]">Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                    />
                  </FormControl>
                  <FormMessage className="text-xs font-light text-red-500" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel className="font-semibold text-[#4A90E2]">Mật khẩu</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="password"
                      placeholder="Mật khẩu"
                      className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                    />
                  </FormControl>
                  <FormMessage className="text-xs font-light text-red-500" />
                </FormItem>
              )}
            />
            {isLoginError && <p className="text-sm font-light text-red-500">{errorMessageLogin}</p>}

            <p
              className="cursor-pointer text-right text-sm text-[#4A90E2] transition-colors duration-200 hover:text-[#3A80D2]"
              onClick={onForgotPassword}
            >
              Quên mật khẩu?
            </p>
            <Button
              disabled={isLoginPending}
              type="submit"
              className="w-full rounded-xl bg-gradient-to-r from-[#4A90E2] to-[#50C878] py-5 text-lg font-semibold text-white shadow-md transition-all duration-300 hover:from-[#3A80D2] hover:to-[#40B868] hover:shadow-lg"
            >
              Đăng nhập
            </Button>

            <div className="text-center">
              <Button
                disabled={isLoginPending}
                variant="link"
                className="text-base text-[#4A90E2] hover:text-[#3A80D2]"
                onClick={onSwitchToRegister}
              >
                Chưa có tài khoản? <span className="font-semibold">Đăng ký ngay</span>
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </div>
  )
}

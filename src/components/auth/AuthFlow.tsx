'use client'
import { AuthMode, AuthStep, UserType } from '@/types/auth'
import React, { useState } from 'react'
import { ForgotPasswordForm } from './ForgotPasswordForm'
import { InitialStep } from './InitialStep'
import { LoginForm } from './LoginForm'
import { RegisterForm } from './RegisterForm'
import { ResetPasswordForm } from './ResetPasswordForm'
import { UserTypeStep } from './UserTypeStep'

const AuthFlow: React.FC = () => {
  const [step, setStep] = useState<AuthStep>(AuthStep.LOGIN)
  const [userType, setUserType] = useState<UserType | null>(null)
  const [authMode, setAuthMode] = useState<AuthMode | null>(null)
  const [resetToken, setResetToken] = useState<string | undefined>(undefined)

  const handleUserTypeSelect = (type: UserType) => {
    setUserType(type)
    setStep(authMode === AuthMode.LOGIN ? AuthStep.LOGIN : AuthStep.REGISTER)
  }

  const handleModeSelect = (mode: AuthMode) => {
    setAuthMode(mode)
    if (mode === AuthMode.LOGIN) {
      setStep(AuthStep.LOGIN)
    } else {
      setStep(AuthStep.USER_TYPE)
    }
  }

  const handleForgotPassword = () => {
    setStep(AuthStep.FORGOT_PASSWORD)
  }

  const handleResetPassword = (token?: string) => {
    setResetToken(token)
    setStep(AuthStep.RESET_PASSWORD)
  }

  const renderCurrentStep = () => {
    switch (step) {
      case AuthStep.INITIAL:
        return <InitialStep onModeSelect={handleModeSelect} />
      case AuthStep.USER_TYPE:
        return <UserTypeStep onUserTypeSelect={handleUserTypeSelect} onBack={() => setStep(AuthStep.INITIAL)} />
      case AuthStep.LOGIN:
        return (
          <LoginForm
            userType={userType}
            onBack={() => setStep(AuthStep.INITIAL)}
            onSwitchToRegister={() => setStep(AuthStep.USER_TYPE)}
            onForgotPassword={handleForgotPassword}
          />
        )
      case AuthStep.REGISTER:
        return (
          <RegisterForm
            userType={userType}
            onBack={() => setStep(AuthStep.USER_TYPE)}
            onSwitchToLogin={() => setStep(AuthStep.LOGIN)}
          />
        )
      case AuthStep.FORGOT_PASSWORD:
        return (
          <ForgotPasswordForm
            userType={userType}
            onBack={() => setStep(AuthStep.LOGIN)}
            onSwitchToLogin={() => setStep(AuthStep.LOGIN)}
            onResetLinkSent={handleResetPassword}
          />
        )
      case AuthStep.RESET_PASSWORD:
        return (
          <ResetPasswordForm
            userType={userType}
            onBack={() => setStep(AuthStep.LOGIN)}
            onSwitchToLogin={() => setStep(AuthStep.LOGIN)}
            token={resetToken}
          />
        )
      default:
        return <InitialStep onModeSelect={handleModeSelect} />
    }
  }

  return <div className="relative flex items-center justify-center p-4">{renderCurrentStep()}</div>
}

export default AuthFlow

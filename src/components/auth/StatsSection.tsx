'use client'
import CountUp from 'react-countup'

const StatsSection = () => {
  return (
    <div className="text-muted-foreground flex items-center justify-center space-x-8 lg:justify-start">
      <div className="text-center">
        <div className="text-2xl font-bold text-[#4A90E2]">
          <CountUp end={1000} duration={2.5} suffix="+" />
        </div>
        <div className="text-sm">Nhiếp ảnh gia</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-[#4A90E2]">
          <CountUp end={5000} duration={2.5} suffix="+" />
        </div>
        <div className="text-sm">Khách hàng</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-[#4A90E2]">
          <CountUp end={10000} duration={2.5} suffix="+" formattingFn={(value) => `${(value / 1000).toFixed(0)}K+`} />
        </div>
        <div className="text-sm">Bộ ảnh</div>
      </div>
    </div>
  )
}

export default StatsSection

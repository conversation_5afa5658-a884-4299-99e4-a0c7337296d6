import { Button } from '@/components/ui/button'
import { CardContent, CardHeader } from '@/components/ui/card'
import { AuthMode } from '@/types/auth'
import { motion } from 'framer-motion'
import React from 'react'

interface InitialStepProps {
  onModeSelect: (mode: AuthMode) => void
}

export const InitialStep: React.FC<InitialStepProps> = ({ onModeSelect }) => {
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, staggerChildren: 0.2 } },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <motion.div className="mx-auto w-full max-w-md" variants={containerVariants} initial="hidden" animate="visible">
      <CardHeader className="space-y-2 text-center">
        <motion.h2
          className="bg-gradient-to-r from-[#4A90E2] to-[#F5A623] bg-clip-text text-3xl font-bold text-transparent"
          variants={itemVariants}
        >
          Chào mừng đến PhotoConnect!
        </motion.h2>
        <motion.p className="text-muted-foreground text-base" variants={itemVariants}>
          Bắt đầu hành trình của bạn ngay hôm nay
        </motion.p>
      </CardHeader>
      <CardContent className="space-y-4">
        <motion.div variants={itemVariants}>
          <Button
            className="w-full rounded-xl bg-gradient-to-r from-[#4A90E2] to-[#50C878] py-6 text-lg font-semibold text-white shadow-md transition-all duration-300 hover:from-[#3A80D2] hover:to-[#40B868] hover:shadow-lg"
            onClick={() => onModeSelect(AuthMode.LOGIN)}
            variant="default"
          >
            Đăng nhập
          </Button>
        </motion.div>
        <motion.div variants={itemVariants}>
          <Button
            className="w-full border-[#4A90E2] py-6 text-lg font-semibold text-[#4A90E2] transition-all duration-300 hover:border-[#3A80D2] hover:bg-[#4A90E2]/10"
            onClick={() => onModeSelect(AuthMode.REGISTER)}
            variant="outline"
          >
            Đăng ký
          </Button>
        </motion.div>
      </CardContent>
    </motion.div>
  )
}

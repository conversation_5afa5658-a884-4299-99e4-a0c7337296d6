import { Button } from '@/components/ui/button'
import { CardContent } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { UserType } from '@/types/auth'
import { zodResolver } from '@hookform/resolvers/zod'
import { Undo2 } from 'lucide-react'
import React from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

const forgotPasswordSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
})

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>

interface ForgotPasswordFormProps {
  userType: UserType | null
  onBack: () => void
  onSwitchToLogin: () => void
  onResetLinkSent?: (token?: string) => void
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({ onBack, onSwitchToLogin, onResetLinkSent }) => {
  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  })

  const onSubmit = (data: ForgotPasswordFormValues) => {
    console.log('Forgot password request submitted:', data)
    if (onResetLinkSent) {
      onResetLinkSent('sample-token')
    }
  }

  return (
    <div className="mx-auto w-full max-w-md">
      <Button
        variant="ghost"
        className="absolute left-0 top-0 flex items-center space-x-2 rounded-lg px-3 py-2 text-[#4A90E2] transition-all duration-300 hover:bg-[#4A90E2]/10 hover:text-[#3A80D2]"
        onClick={onBack}
      >
        <Undo2 size={20} />
        <span className="text-sm font-semibold">Quay lại</span>
      </Button>

      <CardContent className="mt-6 space-y-3">
        <div className="space-y-1 text-center">
          <h2 className="bg-gradient-to-r from-[#4A90E2] to-[#6BA8E8] bg-clip-text text-2xl font-bold text-transparent">
            Quên mật khẩu
          </h2>
          <p className="text-sm text-slate-600">Nhập email của bạn để nhận liên kết đặt lại mật khẩu</p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel className="font-semibold text-[#4A90E2]">Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full rounded-xl bg-gradient-to-r from-[#4A90E2] to-[#50C878] py-5 text-lg font-semibold text-white shadow-md transition-all duration-300 hover:from-[#3A80D2] hover:to-[#40B868] hover:shadow-lg"
            >
              Gửi liên kết đặt lại
            </Button>

            <div className="text-center">
              <Button variant="link" className="text-base text-[#4A90E2] hover:text-[#3A80D2]" onClick={onSwitchToLogin}>
                Quay lại <span className="font-semibold">Đăng nhập</span>
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </div>
  )
}

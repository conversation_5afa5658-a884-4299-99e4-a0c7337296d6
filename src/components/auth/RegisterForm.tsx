import { Button } from '@/components/ui/button'
import { CardHeader } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { formatPhoneNumber } from '@/helper/format'
import { useAuth } from '@/hooks/use-auth'
import { UserType } from '@/types/auth'
import { PhotographerFormValues, RegisterFormValues, baseSchema, photographerSchema } from '@/validation/schema-register'
import { handleChangeInputNumber } from '@/validation/validate'
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, Undo2 } from 'lucide-react'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'

interface RegisterFormProps {
  userType: UserType | null
  onBack: () => void
  onSwitchToLogin: () => void
  onClose?: () => void
}

export const RegisterForm: React.FC<RegisterFormProps> = ({ userType, onBack, onSwitchToLogin }) => {
  const { register, registerStatus, errorMessageRegister } = useAuth()
  const schema = userType === UserType.PHOTOGRAPHER ? photographerSchema : baseSchema
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      username: '',
      email: '',
      phoneNumber: '',
      password: '',
      confirmPassword: '',
      birthDate: '',
      roleId: userType === UserType.PHOTOGRAPHER ? '2' : '1',
      ...(userType === UserType.PHOTOGRAPHER && {
        workplace: '',
        facebookLink: '',
        experience: '',
        equipment: '',
      }),
    },
  })

  const onSubmit = (data: RegisterFormValues) => {
    const baseAccount = {
      username: data.username.trim(),
      email: data.email.trim(),
      password: data.password,
      phoneNumber: data.phoneNumber.replace(/\s/g, ''),
      roleId: data.roleId,
      dateOfBirth: data.birthDate ? new Date(data.birthDate).toISOString() : '',
    }

    if (userType === UserType.PHOTOGRAPHER) {
      const photographerData = data as PhotographerFormValues
      const registerData = {
        account: baseAccount,
        photographer: {
          workplace: photographerData.workplace.trim(),
          facebookLink: photographerData.facebookLink.trim(),
          yearsOfExperience: parseInt(photographerData.experience),
          equipment: photographerData.equipment.trim(),
        },
      }
      register(registerData)
    } else {
      const registerData = { account: baseAccount }
      register(registerData)
    }
  }

  return (
    <div className="mx-auto w-full max-w-md">
      <Button
        variant="ghost"
        className="absolute left-0 top-0 flex items-center space-x-2 rounded-lg px-3 py-2 text-[#4A90E2] transition-all duration-300 hover:bg-[#4A90E2]/10 hover:text-[#3A80D2]"
        onClick={onBack}
      >
        <Undo2 size={20} />
        <span className="text-sm font-semibold">Quay lại</span>
      </Button>
      <CardHeader className="space-y-1 text-center">
        <h2 className="bg-gradient-to-r from-[#4A90E2] to-[#6BA8E8] bg-clip-text text-2xl font-bold text-transparent">
          Trở thành {userType === UserType.CUSTOMER ? 'khách chụp hình' : 'nhiếp ảnh gia'}
        </h2>
      </CardHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-3">
            <div className="flex flex-col gap-6 md:flex-row">
              <div className="w-full md:w-[50%]">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Tên đăng nhập</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="username"
                          className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                        />
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full md:w-[50%]">
                <FormField
                  control={form.control}
                  name="birthDate"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Ngày sinh</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="date"
                          className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                        />
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <FormLabel className="font-semibold text-[#4A90E2]">Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                    />
                  </FormControl>
                  <FormMessage className="text-xs font-light text-red-500" />
                </FormItem>
              )}
            />

            <div className="flex flex-col gap-6 md:flex-row">
              <div className="relative w-full md:w-[50%]">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Mật khẩu</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showPassword ? 'text' : 'password'}
                            className="w-full rounded-xl border-[#4A90E2]/50 p-5 pr-12 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                          />
                          <button
                            type="button"
                            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
              </div>
              <div className="relative w-full md:w-[50%]">
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Xác nhận mật khẩu</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showConfirmPassword ? 'text' : 'password'}
                            className="w-full rounded-xl border-[#4A90E2]/50 p-5 pr-12 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                          />
                          <button
                            type="button"
                            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? <Eye size={20} /> : <EyeOff size={20} />}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="flex flex-col gap-6 md:flex-row">
              <div className={`${userType === UserType.PHOTOGRAPHER ? 'md:w-[50%]' : ''} w-full`}>
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Số điện thoại</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="tel"
                          placeholder="0987 654 321 hoặc +84 987 654 321"
                          onChange={(e) => {
                            const formattedValue = formatPhoneNumber(e.target.value)
                            field.onChange(formattedValue)
                          }}
                          className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                        />
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
              </div>

              {userType === UserType.PHOTOGRAPHER && (
                <div className="w-full md:w-[50%]">
                  <FormField
                    control={form.control}
                    name="experience"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="font-semibold text-[#4A90E2]">Kinh nghiệm (năm)</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="text"
                            value={field.value || ''}
                            placeholder="Ex: 2"
                            onChange={(e) => handleChangeInputNumber(e, field.onChange)}
                            className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                          />
                        </FormControl>
                        <FormMessage className="text-xs font-light text-red-500" />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>
            {userType === UserType.PHOTOGRAPHER && (
              <>
                <FormField
                  control={form.control}
                  name="workplace"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Địa điểm làm việc</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                        />
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="facebookLink"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Link Facebook</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://facebook.com/username"
                          className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                        />
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="equipment"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-semibold text-[#4A90E2]">Thiết bị chụp</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="VD: Sony A7S3 + Lens Sigma 24-70A"
                          className="rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20"
                        />
                      </FormControl>
                      <FormMessage className="text-xs font-light text-red-500" />
                    </FormItem>
                  )}
                />
              </>
            )}
          </div>
          <Button
            type="submit"
            disabled={registerStatus.isLoading}
            className="mt-4 w-full rounded-xl bg-gradient-to-r from-[#4A90E2] to-[#50C878] py-5 text-lg font-semibold text-white shadow-md transition-all duration-300 hover:from-[#3A80D2] hover:to-[#40B868] hover:shadow-lg"
          >
            {registerStatus.isLoading ? 'Đang đăng ký...' : 'Đăng ký'}
          </Button>
          {registerStatus.isError && <p className="mt-2 text-start text-xs text-red-500">Lỗi: {errorMessageRegister}</p>}
        </form>
      </Form>

      <Button variant="link" className="text-sm text-[#4A90E2] hover:text-[#3A80D2]" onClick={onSwitchToLogin}>
        Đã có tài khoản? <span className="font-semibold">Đăng nhập</span>
      </Button>
    </div>
  )
}

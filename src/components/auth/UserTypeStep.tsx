import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card'
import { UserType } from '@/types/auth'
import { Camera, Undo2, User } from 'lucide-react'
import React from 'react'

interface UserTypeStepProps {
  onUserTypeSelect: (type: UserType) => void
  onBack?: () => void
  onClose?: () => void
}

export const UserTypeStep: React.FC<UserTypeStepProps> = ({ onUserTypeSelect, onBack }) => {
  return (
    <div className="mx-auto w-full max-w-md">
      <Button
        variant="ghost"
        className="absolute left-0 top-0 flex items-center space-x-2 rounded-lg px-3 py-2 text-[#4A90E2] transition-all duration-300 hover:bg-[#4A90E2]/10 hover:text-[#3A80D2]"
        onClick={onBack}
      >
        <Undo2 size={20} />
        <span className="text-sm font-semibold">Quay lại</span>
      </Button>
      <CardHeader className="space-y-2 text-center">
        <h2 className="bg-gradient-to-r from-[#4A90E2] to-[#F5A623] bg-clip-text text-3xl font-bold text-transparent">
          Bạn là ai?
        </h2>
        <p className="text-muted-foreground text-base">Chọn vai trò phù hợp với bạn</p>
      </CardHeader>
      <CardContent className="space-y-6">
        <Button
          className="flex h-28 w-full flex-col items-center justify-center space-y-3 rounded-xl border-[#4A90E2] bg-[#F5F7FA]/50 text-[#4A90E2] transition-all duration-300 hover:border-[#3A80D2] hover:bg-[#4A90E2]/10"
          onClick={() => onUserTypeSelect(UserType.CUSTOMER)}
          variant="outline"
        >
          <User size={28} className="text-[#4A90E2]" />
          <span className="text-lg font-semibold">Khách chụp hình</span>
        </Button>
        <Button
          className="flex h-28 w-full flex-col items-center justify-center space-y-3 rounded-xl border-[#4A90E2] bg-[#F5F7FA]/50 text-[#4A90E2] transition-all duration-300 hover:border-[#3A80D2] hover:bg-[#4A90E2]/10"
          onClick={() => onUserTypeSelect(UserType.PHOTOGRAPHER)}
          variant="outline"
        >
          <Camera size={28} className="text-[#4A90E2]" />
          <span className="text-lg font-semibold">Nhiếp ảnh gia</span>
        </Button>
      </CardContent>
    </div>
  )
}

'use client'

import { useAuth } from '@/hooks/use-auth'
import UseProtectedData from '@/hooks/useProtectedData'

const ClientProtected = () => {
  const { logout, isLogoutPending } = useAuth()
  const { useProtectedData } = UseProtectedData
  const { data, error, isLoading } = useProtectedData()

  if (isLoading) return <p>Loading...</p>
  if (error) return <p>Error: {error.message}</p>

  return (
    <div>
      <h1>Protected Data</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
      <button onClick={() => logout()} disabled={isLogoutPending}>
        {isLogoutPending ? 'Logging out...' : 'Logout'}
      </button>
    </div>
  )
}

export default ClientProtected

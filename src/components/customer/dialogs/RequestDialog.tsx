import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { PostDetail } from '@/types/post-schema.type'

export const RequestsDialog = ({
  post,
  handleSelectPhotographer,
}: {
  post: PostDetail
  handleSelectPhotographer: (requestId: string, photographerId: string) => void
}) => {
  const hasRequests = (post.PhotographerRequest || []).length > 0

  const renderRequestStatus = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Chờ phản hồi'
      case 'ACCEPTED':
        return 'Đã chấp nhận'
      case 'REJECTED':
        return 'Bị từ chối'
      default:
        return 'Không xác định'
    }
  }

  const renderBadgeClass = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'border-yellow-600 text-yellow-600'
      case 'ACCEPTED':
        return 'border-green-600 text-green-600'
      case 'REJECTED':
        return 'border-red-600 text-red-600'
      default:
        return 'border-gray-500 text-gray-500'
    }
  }

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Danh sách nhiếp ảnh gia cho {post.title}</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        {hasRequests ? (
          (post.PhotographerRequest ?? []).map((req) => {
            const photographerUsername = req.photographer?.account?.username || 'Nhiếp ảnh gia'
            const photographerPhone = req.photographer?.account?.phoneNumber || 'Chưa có số'
            const badgeClass = renderBadgeClass(req.status || 'PENDING')
            const requestStatus = renderRequestStatus(req.status || 'PENDING')

            return (
              <div key={req.id} className="flex items-center justify-between border-b p-2">
                <div className="flex gap-4">
                  <div>
                    <p className="font-semibold">{photographerUsername}</p>
                    <p className="text-sm text-gray-500">{photographerPhone}</p>
                  </div>
                  <Badge variant="outline" className={badgeClass}>
                    {requestStatus}
                  </Badge>
                </div>
                {req.status === 'PENDING' && (
                  <div className="space-x-2">
                    <Button
                      onClick={() =>
                        req.photographerId && req.id && handleSelectPhotographer(req.id, req.photographerId)
                      }
                      className="bg-green-500 hover:bg-green-600"
                    >
                      Chọn
                    </Button>
                    {/* <Button
                      onClick={() => console.log(`Từ chối ${req.id}`)}
                      variant="outline"
                      className="border-red-500 text-red-500"
                    >
                      Từ chối
                    </Button> */}
                  </div>
                )}
              </div>
            )
          })
        ) : (
          <p className="text-center text-gray-500">Chưa có yêu cầu nào</p>
        )}
      </div>
    </DialogContent>
  )
}

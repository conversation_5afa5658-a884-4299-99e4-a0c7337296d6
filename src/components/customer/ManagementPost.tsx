'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { usePost } from '@/hooks/use-post'
import { useUserStore } from '@/stores/auth.store'
import { PostDetail, PostDetailViewType } from '@/types/post-schema.type'
import { Loader2, Search, Users } from 'lucide-react'
import { useState } from 'react'
import { RequestsDialog } from './dialogs/RequestDialog'

const ManagementPost = () => {
  const { user } = useUserStore()

  const { useFindAllPosts, choosePhotographer } = usePost()
  const [filters, setFilters] = useState({
    search: '',
    categories: [] as string[],
    location: '',
  })
  const [selectedPostId, setSelectedPostId] = useState<string | undefined>(undefined)
  const [selectedPost, setSelectedPost] = useState<PostDetail>()
  const [openRequestsDialog, setOpenRequestsDialog] = useState(false)

  const {
    data: allData,
    isLoading,
    isError,
    error,
    refetch,
  } = useFindAllPosts({
    page: 1,
    limit: 10,
    sort: 'postDate:desc',
    search: filters.search || undefined,
    includeDetails: true,
    status: 'ALL',
    accountId: user?.id,
  })

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({ ...prev, search: e.target.value }))
  }

  const filterPostsByStatus = (status: string | null) => {
    if (!allData?.data?.data) return []
    if (status === 'ALL') return allData.data.data
    return allData.data.data.filter(
      (item: PostDetailViewType) => item.bookingStatus === status || (!item.bookingStatus && status === null),
    )
  }

  const handleSelect = (post: PostDetailViewType) => {
    setSelectedPost(post.post)
    console.log(post)
  }

  const handleSelectPhotographer = (requestId: string, photographerId: string) => {
    console.log(`Chọn nhiếp ảnh gia ${photographerId} cho bài đăng ${selectedPostId}, yêu cầu `)
    choosePhotographer({ postId: selectedPostId!, requestId: requestId! })
    setOpenRequestsDialog(false)
    refetch()
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="mb-6 text-2xl font-bold">Quản lý bài đăng đặt lịch của tôi</h1>

      <div className="mb-6 flex items-center gap-4">
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-500" />
          <Input
            placeholder="Tìm kiếm theo tiêu đề hoặc mô tả..."
            value={filters.search}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>
      </div>

      <Tabs defaultValue="ALL" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="ALL">Tất cả</TabsTrigger>
          <TabsTrigger value="null">Chưa duyệt</TabsTrigger>
          <TabsTrigger value="PENDING">Đang chờ</TabsTrigger>
          <TabsTrigger value="CONFIRMED">Đã xác nhận</TabsTrigger>
          <TabsTrigger value="COMPLETED">Đã hoàn thành</TabsTrigger>
        </TabsList>

        {['ALL', 'null', 'PENDING', 'CONFIRMED', 'COMPLETED'].map((status) => (
          <TabsContent key={status} value={status}>
            {isLoading ? (
              <div className="flex justify-center py-10">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : isError ? (
              <div className="py-4 text-center text-red-500">Lỗi: {error?.message || 'Không thể tải dữ liệu'}</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tiêu đề</TableHead>
                    <TableHead>Ngày đăng</TableHead>
                    <TableHead>Số yêu cầu</TableHead>
                    <TableHead>Trạng thái bài đăng</TableHead>
                    <TableHead>Trạng thái đặt lịch</TableHead>
                    <TableHead>Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterPostsByStatus(status === 'null' ? null : status).length > 0 ? (
                    filterPostsByStatus(status === 'null' ? null : status).map((item: PostDetailViewType) => (
                      <TableRow key={item.post.id}>
                        <TableCell>{item.post.title}</TableCell>
                        <TableCell>{new Date(item.post.postDate ?? new Date()).toLocaleDateString()}</TableCell>
                        <TableCell>{item.totalPhotographerRequests}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              item.post.status === 'APPROVED'
                                ? 'border-green-600 text-green-600'
                                : item.post.status === 'PENDING'
                                  ? 'border-yellow-600 text-yellow-600'
                                  : 'border-red-600 text-red-600'
                            }
                          >
                            {item.post.status === 'PENDING'
                              ? 'Chờ duyệt'
                              : item.post.status === 'APPROVED'
                                ? 'Đang tìm'
                                : item.post.status === 'REJECTED'
                                  ? 'Bị từ chối'
                                  : 'Hết hạn/Đã hoàn thành'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              item.bookingStatus === 'PENDING'
                                ? 'border-yellow-500 text-yellow-500'
                                : item.bookingStatus === 'CONFIRMED'
                                  ? 'border-blue-500 text-blue-500'
                                  : item.bookingStatus === 'COMPLETED'
                                    ? 'border-green-500 text-green-500'
                                    : 'border-gray-500 text-gray-500'
                            }
                          >
                            {item.bookingStatus || 'Chưa đặt'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            {/* <Button variant="outline" size="sm" onClick={() => router.push(`/post/${item.post.id}`)}>
                              Xem chi tiết
                            </Button> */}
                            {(item.totalPhotographerRequests ?? 0) > 0 &&
                              !item.isBooked &&
                              item.post.status === 'APPROVED' && (
                                <Dialog
                                  open={openRequestsDialog && selectedPostId === item.post.id}
                                  onOpenChange={setOpenRequestsDialog}
                                >
                                  <DialogTrigger asChild>
                                    <Button
                                      size="sm"
                                      onClick={() => {
                                        handleSelect(item)
                                        setSelectedPostId(item.post.id)
                                      }}
                                      className="bg-blue-500 text-white hover:bg-blue-600"
                                    >
                                      <Users className="mr-2 h-4 w-4" />
                                      Xem yêu cầu ({item.totalPhotographerRequests})
                                    </Button>
                                  </DialogTrigger>
                                  {selectedPost && (
                                    <RequestsDialog
                                      post={selectedPost}
                                      handleSelectPhotographer={handleSelectPhotographer}
                                    />
                                  )}
                                </Dialog>
                              )}
                            {/* <Button variant="destructive" size="sm">
                              Xóa
                            </Button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center">
                        Không có bài đăng nào
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}

export default ManagementPost

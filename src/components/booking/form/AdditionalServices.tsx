'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { photographyServices } from '@/constants/photograp-service'
import { AdditionalService } from '@/types/post-schema.type'
import { Camera, Info, PlusCircle, X } from 'lucide-react'
import { useState } from 'react'
import { useFormContext } from 'react-hook-form'

const AdditionalServicesSelection = () => {
  const { control, setValue, watch } = useFormContext()
  const [inputValue, setInputValue] = useState('')
  const selectedServices: AdditionalService[] = watch('additionalServices') || []

  const toggleService = (service: AdditionalService, checked: boolean) => {
    if (checked) {
      if (!selectedServices.some((s) => s.id === service.id)) {
        setValue('additionalServices', [...selectedServices, service])
      }
    } else {
      setValue(
        'additionalServices',
        selectedServices.filter((s) => s.id !== service.id),
      )
    }
  }

  const addCustomService = () => {
    const newService = {
      id: `custom-${inputValue}`,
      label: inputValue,
    }
    if (inputValue && !selectedServices.some((s) => s.label === newService.label)) {
      setValue('additionalServices', [...selectedServices, newService])
      setInputValue('')
    }
  }

  const removeService = (id: string) => {
    setValue(
      'additionalServices',
      selectedServices.filter((s) => s.id !== id),
    )
  }

  return (
    <FormField
      control={control}
      name="additionalServices"
      render={() => (
        <FormItem className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <FormLabel className="flex items-center gap-2 text-lg font-medium text-brand-text-primary">
              <Camera className="h-5 w-5 text-brand-primary" />
              Dịch vụ nhiếp ảnh đi kèm
            </FormLabel>
            <Badge variant="outline" className="bg-brand-primary/10 text-brand-primary">
              {selectedServices.length} dịch vụ được chọn
            </Badge>
          </div>

          <FormControl>
            <div className="!mt-0 space-y-4">
              <div className="space-y-3">
                <p className="flex items-center gap-2 text-sm font-medium text-brand-text-secondary">
                  <Info className="h-4 w-4" />
                  Chọn dịch vụ phù hợp với buổi chụp của bạn:
                </p>
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                  {photographyServices.map((s) => (
                    <div key={s.id} className={`flex cursor-pointer items-center justify-between rounded-xl`}>
                      <div className="flex items-center gap-3">
                        <Checkbox
                          id={s.id}
                          checked={selectedServices.some((service) => service.id === s.id)}
                          onCheckedChange={(checked) => toggleService(s, checked as boolean)}
                          className="h-5 w-5 border-brand-primary text-brand-primary"
                        />
                        <label htmlFor={s.id} className="cursor-pointer">
                          <div className="font-normal text-brand-text-primary">{s.label}</div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Input tự nhập */}
              <div className="space-y-3">
                <p className="flex items-center gap-2 text-sm font-medium text-brand-text-secondary">
                  <PlusCircle className="h-4 w-4" />
                  Yêu cầu dịch vụ khác:
                </p>
                <div className="flex gap-2">
                  <Input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Nhập dịch vụ nhiếp ảnh bạn cần (VD: Flycam, Video)"
                    className="h-12 rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3"
                  />
                  <Button
                    type="button"
                    onClick={addCustomService}
                    disabled={!inputValue}
                    className="bg-brand-primary text-white hover:bg-brand-primary/90"
                  >
                    Thêm
                  </Button>
                </div>
              </div>

              {/* Danh sách dịch vụ đã chọn */}
              {selectedServices.length > 0 && (
                <div className="space-y-2 rounded-xl border border-brand-primary/20 bg-brand-primary/5 p-3">
                  <h3 className="flex items-center gap-2 font-medium text-brand-primary">
                    <Camera className="h-5 w-5" />
                    Dịch vụ nhiếp ảnh đã chọn:
                  </h3>
                  <div className="divide-y divide-gray-100">
                    {selectedServices.map((service) => (
                      <div key={service.id} className="flex items-center justify-between py-1">
                        <span className="font-medium text-gray-800">{service.label}</span>
                        <div className="flex items-center gap-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeService(service.id)}
                            className="h-8 w-8 rounded-full p-0 text-gray-400 hover:bg-red-50 hover:text-red-500"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </FormControl>

          <FormMessage className="text-sm text-brand-error" />
        </FormItem>
      )}
    />
  )
}

export default AdditionalServicesSelection

'use client'

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { useFormContext } from 'react-hook-form'

const BudgetRange = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext()

  const formatNumber = (value: string | number | undefined) => {
    if (!value) return ''
    return Number(value).toLocaleString('vi-VN')
  }

  const parseNumber = (value: string) => {
    return Number(value.replace(/\D/g, '')) || ''
  }

  return (
    <div className="space-y-2">
      <FormLabel className="text-lg font-medium text-brand-text-primary">Chi phí mong muốn (VNĐ)</FormLabel>
      <div className="flex flex-wrap gap-4">
        <FormField
          control={control}
          name="minBudget"
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <Input
                  {...field}
                  type="text"
                  placeholder="Từ (ví dụ: 1.000.000)"
                  value={formatNumber(field.value)}
                  onChange={(e) => {
                    const rawValue = parseNumber(e.target.value)
                    field.onChange(rawValue)
                  }}
                  className={cn(
                    'h-12 w-full rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 text-brand-text-primary transition-all duration-300',
                    'hover:border-brand-primary/50',
                    field.value && 'border-brand-primary/50',
                    errors?.minBudget && 'border-red-500 bg-red-500/10',
                  )}
                />
              </FormControl>
              <FormMessage className="text-sm text-brand-error" />
            </FormItem>
          )}
        />
        <span className="text-brand-text-secondary">đến</span>
        <FormField
          control={control}
          name="maxBudget"
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <Input
                  {...field}
                  type="text"
                  placeholder="Đến (ví dụ: 10.000.000)"
                  value={formatNumber(field.value)}
                  onChange={(e) => {
                    const rawValue = parseNumber(e.target.value)
                    field.onChange(rawValue)
                  }}
                  className={cn(
                    'h-12 w-full rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 text-brand-text-primary transition-all duration-300',
                    'hover:border-brand-primary/50',
                    field.value && 'border-brand-primary/50',
                    errors?.maxBudget && 'border-red-500 bg-red-500/10',
                  )}
                />
              </FormControl>
              <FormMessage className="text-sm text-brand-error" />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}

export default BudgetRange

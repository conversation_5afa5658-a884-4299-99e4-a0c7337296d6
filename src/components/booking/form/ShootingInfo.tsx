'use client'

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import { useFormContext } from 'react-hook-form'

const ShootingInfo = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext()

  return (
    <>
      <FormField
        control={control}
        name="title"
        render={({ field }) => (
          <FormItem className="flex flex-col gap-2">
            <FormLabel className="text-lg font-medium text-brand-text-primary">
              Tiêu đề buổi chụp <span className="text-brand-error">*</span>
            </FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Nhập tiêu đề buổi chụp (ví dụ: <PERSON><PERSON><PERSON>nh cưới, <PERSON>ụp lookbook...)"
                className={cn(
                  'h-12 w-full rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 text-brand-text-primary transition-all duration-300',
                  'hover:border-brand-primary/50',
                  field.value && 'border-brand-primary/50',
                  errors?.title && 'border-red-500 bg-red-500/10',
                )}
              />
            </FormControl>
            <FormMessage className="text-sm text-brand-error" />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex flex-col gap-2">
            <FormLabel className="text-lg font-medium text-brand-text-primary">
              Nội dung buổi chụp <span className="text-brand-error">*</span>
            </FormLabel>
            <FormControl>
              <Textarea
                {...field}
                placeholder="Mô tả chi tiết về buổi chụp (ví dụ: phong cách, số lượng ảnh mong muốn...)"
                className={cn(
                  'w-full rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 text-brand-text-primary transition-all duration-300',
                  'hover:border-brand-primary/50',
                  field.value && 'border-brand-primary/50',
                  errors?.description && 'border-red-500 bg-red-500/10',
                )}
              />
            </FormControl>
            <FormMessage className="text-sm text-brand-error" />
          </FormItem>
        )}
      />
    </>
  )
}

export default ShootingInfo

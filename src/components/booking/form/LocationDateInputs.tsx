'use client'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import locationOptions from '@/constants/locations'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { CalendarIcon, Check, ChevronsUpDown } from 'lucide-react'
import { useState } from 'react'
import { useFormContext } from 'react-hook-form'

const LocationAndDateInputs = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext()
  const [open, setOpen] = useState(false)

  return (
    <div className="space-y-6">
      <FormField
        control={control}
        name="location"
        render={({ field }) => {
          return (
            <FormItem className="flex flex-col gap-2">
              <FormLabel className="text-lg font-medium text-brand-text-primary">
                Địa điểm chụp <span className="text-brand-error">*</span>
              </FormLabel>
              <FormControl>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        'h-12 w-full justify-between rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 font-normal text-brand-text-primary transition-all duration-300',
                        'hover:border-brand-primary/50',
                        field.value && 'border-brand-primary/50',
                        errors?.location && 'border-red-500 bg-red-500/10',
                      )}
                    >
                      {field.value
                        ? locationOptions.find((option) => option.id === field.value)?.name
                        : 'Chọn địa điểm chụp (ví dụ: Hà Nội, Đà Lạt...)'}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                    <Command className="w-full">
                      <CommandInput placeholder="Tìm kiếm địa điểm..." className="w-full" />
                      <CommandList className="max-h-[300px] w-full overflow-y-auto">
                        <CommandEmpty>Không tìm thấy địa điểm.</CommandEmpty>
                        <CommandGroup>
                          {locationOptions.map((option) => (
                            <CommandItem
                              key={option.id}
                              value={option.name}
                              onSelect={() => {
                                field.onChange(option.id)
                                setOpen(false)
                              }}
                            >
                              <Check
                                className={cn('mr-2 h-4 w-4', field.value === option.id ? 'opacity-100' : 'opacity-0')}
                              />
                              {option.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </FormControl>
              <FormMessage className="text-sm text-brand-error" />
            </FormItem>
          )
        }}
      />

      <FormField
        control={control}
        name="detailedLocation"
        render={({ field }) => (
          <FormItem className="flex flex-col gap-2">
            <FormLabel className="text-lg font-medium text-brand-text-primary">Địa điểm chi tiết (nếu có)</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Nhập địa điểm chi tiết (ví dụ: Hồ Gươm, Đà Lạt Square...)"
                className={cn(
                  'h-12 w-full rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 text-brand-text-primary transition-all duration-300',
                  'hover:border-brand-primary/50',
                  field.value && 'border-brand-primary/50',
                )}
              />
            </FormControl>
            <FormMessage className="text-sm text-brand-error" />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="shootingDate"
        render={({ field }) => (
          <FormItem className="flex flex-col gap-2">
            <FormLabel className="text-lg font-medium text-brand-text-primary">
              Ngày chụp dự kiến <span className="text-brand-error">*</span>
            </FormLabel>
            <FormControl>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'h-12 w-full justify-start rounded-xl border-brand-text-secondary/20 bg-white/90 px-4 py-3 text-left text-brand-text-primary transition-all duration-300',
                      'hover:border-brand-primary/50 hover:bg-white',
                      !field.value && 'text-brand-text-secondary',
                      field.value && 'border-brand-primary/50',
                      errors?.shootingDate && 'border-red-500 bg-red-500/10',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-5 w-5 text-brand-primary" />
                    {field.value ? format(field.value, 'PPP', { locale: vi }) : <span>Chọn ngày chụp</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={(date) => {
                      field.onChange(date)
                    }}
                    initialFocus
                    locale={vi}
                    className="rounded-xl border-brand-text-secondary/20 bg-white shadow-lg"
                  />
                </PopoverContent>
              </Popover>
            </FormControl>
            <FormMessage className="text-sm text-brand-error" />
          </FormItem>
        )}
      />
    </div>
  )
}

export default LocationAndDateInputs

'use client'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { backgroundOptions } from '@/constants/background-options'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { useFormContext } from 'react-hook-form'

const BackgroundSelection = () => {
  const { control, setValue } = useFormContext()

  return (
    <FormField
      control={control}
      name="background"
      render={({ field }) => (
        <FormItem className="space-y-4">
          <FormLabel className="text-lg font-medium text-brand-text-primary">
            Chọ<PERSON> b<PERSON><PERSON> cảnh <span className="text-brand-error">*</span>
          </FormLabel>
          <FormControl>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
              {backgroundOptions.map((option) => (
                <div
                  key={option.id}
                  className={cn(
                    'group relative cursor-pointer overflow-hidden rounded-xl transition-all duration-300',
                    field.value === option.id
                      ? 'scale-[1.02] transform ring-4 ring-brand-primary ring-offset-2'
                      : 'hover:-translate-y-1 hover:scale-[1.03] hover:transform hover:shadow-xl',
                  )}
                  onClick={() => {
                    field.onChange(option.id) // Cập nhật giá trị vào field
                    setValue('background', option.id) // Đảm bảo form context được cập nhật
                  }}
                >
                  <div className="relative h-[140px] w-full">
                    <Image
                      src={option.image || '/placeholder.svg'}
                      alt={option.name}
                      fill
                      className={cn(
                        'object-cover transition-transform duration-500',
                        field.value === option.id ? 'scale-105' : 'group-hover:scale-110',
                      )}
                    />
                    <div className="absolute inset-0 flex items-end bg-gradient-to-t from-black/70 via-black/30 to-transparent p-4">
                      <h4 className="text-lg font-medium text-white">{option.name}</h4>
                    </div>

                    <div
                      className={cn(
                        'absolute inset-0 transition-opacity duration-300',
                        field.value === option.id ? 'bg-brand-primary/10' : 'bg-black/0 group-hover:bg-brand-primary/5',
                      )}
                    />

                    {field.value === option.id && (
                      <div className="absolute right-3 top-3 rounded-full bg-brand-primary p-1 text-white shadow-lg duration-200 animate-in zoom-in-75">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="3"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polyline points="20 6 9 17 4 12"></polyline>
                        </svg>
                      </div>
                    )}
                  </div>

                  {field.value !== option.id && (
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                      <div className="translate-y-8 transform rounded-full bg-white/80 px-3 py-1 text-sm font-medium text-brand-primary shadow-lg transition-transform duration-300 group-hover:translate-y-0">
                        Chọn
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </FormControl>
          <FormMessage className="text-sm text-brand-error" />
        </FormItem>
      )}
    />
  )
}

export default BackgroundSelection

'use client'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { categoryOptions } from '@/constants/category-options'
import { cn } from '@/lib/utils'
import { ChevronLeft, ChevronRight, Info } from 'lucide-react'
import Image from 'next/image'
import { useRef, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { Swiper as SwiperInstance } from 'swiper'
import 'swiper/css'
import 'swiper/css/navigation'
import { Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react'
import AnimatedHint from '../common/AnimatedHint'

const CategorySelection = () => {
  const { control } = useFormContext()
  const [hasScrolled, setHasScrolled] = useState(false)
  const swiperRef = useRef<SwiperInstance | null>(null)

  return (
    <FormField
      control={control}
      name="category"
      render={({ field }) => (
        <FormItem className="space-y-4">
          <div className="flex items-center">
            <FormLabel className="text-lg font-medium text-brand-text-primary">
              Chọn thể loại <span className="text-brand-error">*</span>
            </FormLabel>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button className="ml-2 text-brand-text-secondary transition-colors hover:text-brand-primary">
                    <Info className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-80 text-sm">Chọn thể loại chụp ảnh phù hợp với nhu cầu của bạn</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="flex items-center">
            <div className="flex items-center space-x-2">
              <div className="cursor-pointer transition-all duration-300 hover:scale-110">
                <Info className="h-4 w-4 text-brand-primary" />
              </div>
              <Label className="text-sm text-brand-text-secondary">
                Thông tin chi tiết về các thể loại chụp, xem tại{' '}
                <a href="#" className="font-medium text-brand-primary transition-colors hover:underline">
                  đây
                </a>
              </Label>
            </div>
          </div>

          <FormControl>
            <div className="relative px-2">
              <Swiper
                modules={[Navigation]}
                spaceBetween={16}
                slidesPerView={4}
                navigation={{
                  prevEl: '.swiper-button-prev-custom',
                  nextEl: '.swiper-button-next-custom',
                }}
                className="swiper-container -mx-2 px-2 py-2"
                onSlideChange={() => setHasScrolled(true)}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
              >
                {categoryOptions.map((option) => (
                  <SwiperSlide key={option.id} className="w-[180px] flex-shrink-0 snap-start">
                    <div
                      className={cn(
                        'group relative cursor-pointer overflow-hidden rounded-xl transition-all duration-300',
                        field.value === option.id
                          ? 'scale-[1.02] transform'
                          : 'hover:z-10 hover:scale-[1.02] hover:transform hover:shadow-lg',
                      )}
                      onClick={() => field.onChange(option.id)} // Cập nhật giá trị vào field
                    >
                      <div className="relative h-[180px] w-full">
                        <Image
                          src={option.image || '/placeholder.svg'}
                          alt={option.name}
                          fill
                          className={cn(
                            'object-cover transition-transform duration-500',
                            field.value === option.id ? 'scale-105' : 'group-hover:scale-110',
                          )}
                        />
                        <div className="absolute inset-0 flex items-end bg-gradient-to-t from-black/70 via-black/30 to-transparent p-4">
                          <h4 className="text-lg font-medium text-white">{option.name}</h4>
                        </div>
                        <div
                          className={cn(
                            'absolute inset-0 transition-opacity duration-300',
                            field.value === option.id
                              ? 'bg-brand-primary/10'
                              : 'bg-black/0 group-hover:bg-brand-primary/5',
                          )}
                        />
                        {field.value === option.id && (
                          <div className="absolute right-3 top-3 rounded-full bg-brand-primary p-1 text-white shadow-lg duration-200 animate-in zoom-in-75">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="3"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                          </div>
                        )}
                      </div>
                      {field.value !== option.id && (
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                          <div className="-translate-y-8 transform rounded-full bg-white/80 px-3 py-1 text-sm font-medium text-brand-primary shadow-lg transition-transform duration-300 group-hover:translate-y-0">
                            Chọn
                          </div>
                        </div>
                      )}
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
              <button
                className="swiper-button-prev-custom absolute left-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white/90 p-2 shadow-lg transition-all duration-300 hover:scale-105 hover:bg-white hover:shadow-xl focus:outline-none focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Scroll left"
              >
                <ChevronLeft className="h-6 w-6 text-brand-text-primary" />
              </button>
              <button
                className="swiper-button-next-custom absolute right-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white/90 p-2 shadow-lg transition-all duration-300 hover:scale-105 hover:bg-white hover:shadow-xl focus:outline-none focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Scroll right"
              >
                <ChevronRight className="h-6 w-6 text-brand-text-primary" />
              </button>
              {!hasScrolled && (
                <AnimatedHint
                  message="Cuộn để xem thêm tùy chọn"
                  className="right-12 top-1/2 -translate-y-1/2"
                  onDismiss={() => setHasScrolled(true)}
                />
              )}
            </div>
          </FormControl>
          <FormMessage className="text-sm text-brand-error" />
        </FormItem>
      )}
    />
  )
}

export default CategorySelection

import { BackgroundSelection, CategorySelection, LocationAndDateInputs } from '@/components/booking/form'
import { Button } from '@/components/ui/button'
import { usePostFormStore } from '@/stores/booking.store'
import { CURRENTSTEP } from '@/types/post-schema.type'
import { basicInfoSchema, BasicInfoValues } from '@/validation/schema-post'
import { zodResolver } from '@hookform/resolvers/zod'
import { ArrowRight } from 'lucide-react'
import { useEffect } from 'react'
import { FormProvider, useForm, useWatch } from 'react-hook-form'

const BasicInfoForm = () => {
  const { setSelectedStep, setPostData, postDetail } = usePostFormStore((state) => state)

  const form = useForm<BasicInfoValues>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      category: postDetail.category || '',
      background: postDetail.background || '',
      location: postDetail.location || '',
      detailedLocation: postDetail.detailedLocation || '',
      shootingDate: postDetail.shootingDate ? new Date(postDetail.shootingDate) : undefined,
    },
    mode: 'onChange',
  })

  const formValues = useWatch({
    control: form.control,
  })

  useEffect(() => {
    if (!formValues) return

    const updateData: Record<string, unknown> = {}
    Object.entries(formValues).forEach(([key, value]) => {
      if (key === 'shootingDate' && value) {
        updateData[key] = new Date(value).toISOString()
      } else {
        updateData[key] = value
      }
    })

    if (Object.keys(updateData).length > 0) {
      setPostData(updateData)
    }
  }, [formValues, setPostData])

  async function onSubmit(values: BasicInfoValues) {
    const isoDate = new Date(values.shootingDate).toISOString()

    setPostData({
      category: values.category,
      background: values.background,
      location: values.location,
      detailedLocation: values.detailedLocation,
      shootingDate: isoDate,
    })

    setSelectedStep(CURRENTSTEP.DetailsInfo)
  }

  return (
    <div>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <CategorySelection />
            <BackgroundSelection />
            <LocationAndDateInputs />
            <div className="flex items-center justify-between pt-6">
              <div className="invisible">
                <Button variant="outline">Quay lại</Button>
              </div>
              <Button className="bg-brand-primary text-white hover:bg-brand-primary/90">
                Tiếp theo
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  )
}

export default BasicInfoForm

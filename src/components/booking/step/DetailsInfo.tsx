// components/booking/DetailsInfoForm.tsx
import { AdditionalServicesSelection, BudgetRange, ShootingInfo } from '@/components/booking/form'
import { Button } from '@/components/ui/button'
import { usePost } from '@/hooks/use-post'
import { useUserStore } from '@/stores/auth.store'
import { usePostFormStore } from '@/stores/booking.store'
import { CURRENTSTEP } from '@/types/post-schema.type'
import { detailsInfoSchema, DetailsInfoValues } from '@/validation/schema-post'
import { zodResolver } from '@hookform/resolvers/zod'
import { ArrowLeft, ArrowRight, Loader2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import PhoneConfirmationDialog from '../dialogs/PhoneConfirmation'

const DetailsInfoForm = () => {
  const user = useUserStore.getState().user
  const { setSelectedStep, setPostData, postDetail, resetStore } = usePostFormStore((state) => state)
  const { createPost, isLoading, isError, error, useFindAllPosts } = usePost()
  const { refetch } = useFindAllPosts()
  const [openDialog, setOpenDialog] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState(user?.phoneNumber || '')

  const form = useForm<DetailsInfoValues>({
    resolver: zodResolver(detailsInfoSchema),
    defaultValues: {
      title: postDetail.title || '',
      description: postDetail.description || '',
      additionalServices: postDetail.additionalServices || [],
      minBudget: postDetail.minBudget || 0,
      maxBudget: postDetail.maxBudget || 0,
    },
    mode: 'onChange',
  })

  const formValues = useWatch({ control: form.control })

  useEffect(() => {
    if (!formValues) return
    const updateData: Record<string, unknown> = {}
    Object.entries(formValues).forEach(([key, value]) => {
      updateData[key] = value
    })
    if (Object.keys(updateData).length > 0) {
      setPostData(updateData)
    }
  }, [formValues, setPostData])

  const onSubmit = (values: DetailsInfoValues) => {
    setPostData({
      title: values.title,
      description: values.description,
      additionalServices: values.additionalServices,
      minBudget: values.minBudget,
      maxBudget: values.maxBudget,
    })
    setOpenDialog(true)
  }

  const handlePhoneConfirm = (confirmedPhone: string) => {
    setPhoneNumber(confirmedPhone)
    if (formValues) {
      createPost({ ...postDetail, contactPhone: phoneNumber })
    }
    resetStore()
    refetch()
  }

  return (
    <div>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <ShootingInfo />
            <AdditionalServicesSelection />
            <BudgetRange />
          </div>

          {isError && (
            <div className="mt-4 flex items-center justify-between rounded-md bg-red-100 p-4 text-red-800">
              <span>{error?.message || 'Không thể tạo bài đăng. Vui lòng thử lại.'}</span>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex items-center justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setSelectedStep(CURRENTSTEP.BasicInfo)}
              disabled={isLoading}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
            <Button
              type="submit"
              className="bg-brand-primary text-white hover:bg-brand-primary/90"
              disabled={isLoading || !form.formState.isValid}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang xử lý...
                </>
              ) : (
                <>
                  Đăng bài
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </form>
      </FormProvider>
      <PhoneConfirmationDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        defaultPhoneNumber={phoneNumber}
        onConfirm={handlePhoneConfirm}
      />
    </div>
  )
}

export default DetailsInfoForm

'use client'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { usePostFormStore } from '@/stores/booking.store'
import { CURRENTSTEP } from '@/types/post-schema.type'
import {
  Calendar,
  Camera,
  CheckCircle2,
  DollarSign,
  FileText,
  ImageIcon,
  MapPin,
  Package,
  Pencil,
  Tag,
} from 'lucide-react'
import { useEffect, useState } from 'react'

const BookingPreview = () => {
  const { postDetail, currentStep } = usePostFormStore((state) => state)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const formatCurrency = (amount: number) => {
    if (!amount && amount !== 0) return ''
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const hasAdditionalServices = postDetail?.additionalServices && postDetail.additionalServices.length > 0

  const basicSections = [
    {
      id: 'category',
      icon: <Tag className="h-5 w-5 text-brand-primary" />,
      label: 'Thể loại chụp',
      value: postDetail?.category || '',
    },
    {
      id: 'background',
      icon: <ImageIcon className="h-5 w-5 text-brand-primary" />,
      label: 'Phông nền',
      value: postDetail?.background || '',
    },
    {
      id: 'location',
      icon: <MapPin className="h-5 w-5 text-brand-primary" />,
      label: 'Địa điểm',
      value: postDetail?.location || '',
      subValue: postDetail?.detailedLocation || '',
    },
    {
      id: 'date',
      icon: <Calendar className="h-5 w-5 text-brand-primary" />,
      label: 'Ngày chụp',
      value: postDetail?.shootingDate
        ? new Date(postDetail.shootingDate).toLocaleDateString('vi-VN', {
            weekday: 'long',
            day: 'numeric',
            month: 'numeric',
            year: 'numeric',
          })
        : '',
    },
  ]

  // Các thông tin chi tiết từ detailsInfoSchema
  const detailSections = [
    {
      id: 'title',
      icon: <Pencil className="h-5 w-5 text-brand-primary" />,
      label: 'Tiêu đề buổi chụp',
      value: postDetail?.title || '',
    },
    {
      id: 'description',
      icon: <FileText className="h-5 w-5 text-brand-primary" />,
      label: 'Mô tả buổi chụp',
      value: postDetail?.description || '',
      isLongText: true,
    },
    {
      id: 'budget',
      icon: <DollarSign className="h-5 w-5 text-brand-primary" />,
      label: 'Ngân sách dự kiến',
      value:
        postDetail?.minBudget && postDetail?.maxBudget
          ? `${formatCurrency(postDetail.minBudget)} - ${formatCurrency(postDetail.maxBudget)}`
          : postDetail?.minBudget
            ? `Từ ${formatCurrency(postDetail.minBudget)}`
            : postDetail?.maxBudget
              ? `Đến ${formatCurrency(postDetail.maxBudget)}`
              : '',
    },
  ]

  return (
    <div className="hidden border-t border-brand-gray_medium p-6 lg:block lg:w-1/3 lg:border-l lg:border-t-0 lg:p-8">
      <div className="sticky top-24 transition-all duration-300">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-medium text-brand-text-primary">Chi tiết đặt lịch</h3>
          {postDetail && Object.keys(postDetail).length > 0 && (
            <Badge
              variant="outline"
              className="border-brand-primary/20 bg-brand-primary/10 font-medium text-brand-primary"
            >
              Xem trước
            </Badge>
          )}
        </div>

        <div className={`transition-all duration-500 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
          {!postDetail || Object.keys(postDetail).length === 0 ? (
            <div className="rounded-lg border border-dashed p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-brand-primary/10">
                <FileText className="h-6 w-6 text-brand-primary" />
              </div>
              <h4 className="mb-2 font-medium text-brand-text-primary">Chưa có thông tin</h4>
              <p className="text-sm text-brand-text-secondary">
                Vui lòng điền thông tin bài đăng ở form bên trái để xem trước chi tiết.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {currentStep === CURRENTSTEP.DetailsInfo && (
                <>
                  <h4 className="flex items-center gap-2 font-medium text-brand-text-primary">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    Chi tiết buổi chụp
                  </h4>

                  {detailSections.map((section) => {
                    return (
                      <div
                        key={section.id}
                        className={`transition-all duration-300 ease-in-out ${section.value ? 'opacity-100' : 'opacity-60'}`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="mt-0.5 flex-shrink-0 rounded-full bg-brand-primary/10 p-1">{section.icon}</div>
                          <div className="flex-grow">
                            <p className="text-sm font-medium text-brand-text-secondary">{section.label}</p>

                            {section.value ? (
                              <p className="mt-1 whitespace-pre-wrap text-brand-text-primary">{section.value}</p>
                            ) : (
                              <div className="mt-1 h-5 w-32 animate-pulse rounded bg-brand-gray_light"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}

                  {hasAdditionalServices && (
                    <div className="space-y-4 rounded-lg border border-gray-100 bg-white p-4 shadow-sm">
                      <h4 className="flex items-center gap-2 font-medium text-brand-text-primary">
                        <Package className="h-4 w-4 text-brand-primary" />
                        Dịch vụ bổ sung
                      </h4>

                      <div className="space-y-2">
                        {postDetail?.additionalServices?.map((service, index) => (
                          <div key={index} className="flex items-center justify-between rounded-md bg-gray-50 p-2">
                            <div className="flex items-center gap-2">
                              <Camera className="h-4 w-4 text-brand-text-secondary" />
                              <span className="text-sm text-brand-text-primary">{service?.label}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <Separator className="my-2" />

                  <p className="mt-3 text-xs text-brand-text-secondary">
                    *Giá cuối cùng có thể thay đổi dựa trên thỏa thuận với nhiếp ảnh gia
                  </p>
                </>
              )}

              {currentStep === CURRENTSTEP.BasicInfo && (
                <>
                  <h4 className="flex items-center gap-2 font-medium text-brand-text-primary">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    Thông tin cơ bản
                  </h4>

                  {basicSections.map((section) => {
                    return (
                      <div
                        key={section.id}
                        className={`transition-all duration-300 ease-in-out ${section.value ? 'opacity-100' : 'opacity-60'}`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="mt-0.5 flex-shrink-0 rounded-full bg-brand-primary/10 p-1">{section.icon}</div>
                          <div className="flex-grow">
                            <p className="text-sm font-medium text-brand-text-secondary">{section.label}</p>

                            {section.value ? (
                              <p className="mt-1 font-medium text-brand-text-primary">{section.value}</p>
                            ) : (
                              <div className="mt-1 h-5 w-32 animate-pulse rounded bg-brand-gray_light"></div>
                            )}

                            {section.subValue && (
                              <p className="mt-1 text-sm italic text-brand-text-secondary">{section.subValue}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default BookingPreview

'use client'

import { cn } from '@/lib/utils'
import { ArrowRight, Hand } from 'lucide-react'
import { useEffect, useState } from 'react'

interface AnimatedHintProps {
  message?: string
  className?: string
  onDismiss?: () => void
  autoDismiss?: boolean
  dismissTimeout?: number
}

export default function AnimatedHint({
  message = 'Cuộn để xem thêm tùy chọn',
  className,
  onDismiss,
  autoDismiss = true,
  dismissTimeout = 5000,
}: AnimatedHintProps) {
  const [visible, setVisible] = useState(true)
  const [hovered, setHovered] = useState(false)

  useEffect(() => {
    if (autoDismiss && !hovered) {
      const timer = setTimeout(() => {
        setVisible(false)
        if (onDismiss) onDismiss()
      }, dismissTimeout)

      return () => clearTimeout(timer)
    }
  }, [autoDismiss, dismissTimeout, onDismiss, hovered])

  if (!visible) return null

  return (
    <div
      className={cn(
        'absolute z-20 rounded-lg bg-white/90 px-4 py-2 text-brand-text-primary shadow-lg backdrop-blur-sm',
        'flex items-center space-x-2 transition-all duration-300',
        hovered ? 'bg-white shadow-xl' : '',
        className,
      )}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <div className="relative">
        <Hand className="animate-bounce-x h-4 w-4 text-brand-primary" />
      </div>
      <span className="text-sm font-medium">{message}</span>
      <ArrowRight className="animate-bounce-x h-4 w-4 text-brand-primary" />

      {hovered && (
        <button
          className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-brand-primary text-xs text-white"
          onClick={() => {
            setVisible(false)
            if (onDismiss) onDismiss()
          }}
        >
          ✕
        </button>
      )}
    </div>
  )
}

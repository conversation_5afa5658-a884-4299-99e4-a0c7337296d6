'use-client'
import React from 'react'

interface Step {
  id: string
  title: string
}

interface BannerCreationProps {
  steps: Step[]
  currentStep: string
}

const BannerCreation: React.FC<BannerCreationProps> = ({ steps, currentStep }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCurrentStep = step.id === currentStep
          const isCompleted = steps.findIndex((s) => s.id === currentStep) > index

          return (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  isCompleted
                    ? 'bg-brand-primary text-white'
                    : isCurrentStep
                      ? 'bg-brand-primary text-white'
                      : 'bg-brand-gray_medium text-brand-text-secondary'
                } font-medium`}
              >
                {index + 1}
              </div>
              <div
                className={`ml-2 font-medium ${
                  isCompleted ? 'text-brand-primary' : isCurrentStep ? 'text-brand-primary' : 'text-brand-text-secondary'
                }`}
              >
                {step.title}
              </div>
              {index < steps.length - 1 && (
                <div className="mx-4 hidden h-1 flex-1 bg-brand-gray_medium md:flex">
                  <div className={`h-full w-0 ${isCompleted ? 'bg-brand-primary' : 'bg-transparent'}`}></div>
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default BannerCreation

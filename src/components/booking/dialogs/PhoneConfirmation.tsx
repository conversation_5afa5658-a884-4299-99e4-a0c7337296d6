import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatPhoneNumber, phoneRegExp } from '@/helper/format'
import React, { useEffect, useRef, useState } from 'react'

interface PhoneConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  defaultPhoneNumber: string
  onConfirm: (phoneNumber: string) => void
}

const validatePhoneNumber = (value: string) => {
  const digitsOnly = value.replace(/[^\d+]/g, '')
  const isStartWithPlus84 = value.startsWith('+84')
  const requiredLength = isStartWithPlus84 ? 12 : 10

  if (digitsOnly.length === 0) {
    return { isValid: false, message: '<PERSON><PERSON> điện thoại là bắt buộc' }
  }
  if (digitsOnly.length > 0 && digitsOnly.length < requiredLength) {
    return { isValid: false, message: 'Số điện thoại chưa đủ số' }
  }
  if (digitsOnly.length > requiredLength) {
    return { isValid: false, message: 'Số điện thoại vượt quá số lượng cho phép' }
  }
  if (!phoneRegExp.test(value)) {
    return {
      isValid: false,
      message: 'Số điện thoại không hợp lệ. Phải bắt đầu bằng 03x, 05x, 07x, 08x, 09x.',
    }
  }
  return { isValid: true, message: '' }
}

const PhoneConfirmationDialog: React.FC<PhoneConfirmationDialogProps> = ({
  open,
  onOpenChange,
  defaultPhoneNumber,
  onConfirm,
}) => {
  const [tempPhoneNumber, setTempPhoneNumber] = useState(defaultPhoneNumber || '')
  const [isEditing, setIsEditing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [isEditing])

  const handlePhoneNumberChange = (value: string) => {
    const formattedValue = formatPhoneNumber(value)
    setTempPhoneNumber(formattedValue)
    const validation = validatePhoneNumber(formattedValue)
    setErrorMessage(validation.message)
  }

  const handleSavePhoneNumber = () => {
    const validation = validatePhoneNumber(tempPhoneNumber)
    if (!validation.isValid) {
      setErrorMessage(validation.message)
      return
    }
    setErrorMessage('')
    setIsEditing(false)
  }

  const handleConfirm = () => {
    const validation = validatePhoneNumber(tempPhoneNumber)
    if (!validation.isValid) {
      setErrorMessage(validation.message)
      return
    }

    onConfirm(tempPhoneNumber)
    onOpenChange(false)
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận số điện thoại</AlertDialogTitle>
          <AlertDialogDescription>
            Xác thực số điện thoại giúp Picnow hỗ trợ bạn tốt nhất trong quá trình đặt lịch chụp hình.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="flex items-center space-x-2">
          <div className="flex-1">
            <label className="text-sm font-medium">Số điện thoại</label>
            <Input
              ref={inputRef}
              type="tel"
              value={tempPhoneNumber}
              onChange={(e) => handlePhoneNumberChange(e.target.value)}
              placeholder="0987 654 321 hoặc +84 987 654 321"
              readOnly={!isEditing}
              className={`mt-1 rounded-xl border-[#4A90E2]/50 p-5 text-base focus:border-[#4A90E2] focus:ring-[#4A90E2]/20 ${
                !isEditing ? 'cursor-not-allowed bg-gray-100' : ''
              } ${errorMessage ? 'border-red-500' : ''}`}
            />
            {errorMessage && <p className="mt-1 text-xs font-light text-red-500">{errorMessage}</p>}
          </div>
          <div className="mt-6">
            {isEditing ? (
              <Button onClick={handleSavePhoneNumber} className="">
                Lưu
              </Button>
            ) : (
              <Button onClick={() => setIsEditing(true)} variant="outline" className="hover:bg-orange-50">
                Thay đổi
              </Button>
            )}
          </div>
        </div>

        {!isEditing && (
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => onOpenChange(false)}>Hủy</AlertDialogCancel>
            <Button onClick={handleConfirm}>Xác nhận</Button>
          </AlertDialogFooter>
        )}
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default PhoneConfirmationDialog

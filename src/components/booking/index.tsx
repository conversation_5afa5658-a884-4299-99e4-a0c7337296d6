'use client'
import { BannerCreation, BookingPreview } from '@/components/booking/common'
import { BasicInfoForm, DetailsInfoForm } from '@/components/booking/step'
import { usePostFormStore } from '@/stores/booking.store'
import { CURRENTSTEP } from '@/types/post-schema.type'
import { useEffect } from 'react'

const Bookings: React.FC = () => {
  const { currentStep } = usePostFormStore((state) => state)

  const steps = [
    { id: CURRENTSTEP.BasicInfo, title: 'Thông Tin Bài Đăng' },
    { id: CURRENTSTEP.DetailsInfo, title: 'Đặt Lịch và Hoàn Tất' },
  ]

  const renderStepContent = () => {
    switch (currentStep) {
      case CURRENTSTEP.BasicInfo:
        return <BasicInfoForm />
      case CURRENTSTEP.DetailsInfo:
        return <DetailsInfoForm />
      default:
        return null
    }
  }

  useEffect(() => {
    window.scrollTo(0, 0)
  }, [currentStep])

  return (
    <div>
      <div className="flex flex-col lg:flex-row">
        <div className="p-6 md:p-8 lg:w-2/3">
          <BannerCreation steps={steps} currentStep={currentStep} />
          {renderStepContent()}
        </div>
        <BookingPreview />
      </div>
    </div>
  )
}

export default Bookings

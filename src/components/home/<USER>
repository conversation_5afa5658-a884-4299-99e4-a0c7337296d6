'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Calendar, Camera, MapPin, Search } from 'lucide-react'
import { useState } from 'react'

export default function SearchBar() {
  const [date, setDate] = useState<Date | undefined>(undefined)
  const [category, setCategory] = useState<string | undefined>(undefined)
  const [location, setLocation] = useState<string | undefined>(undefined)

  const handleSearch = () => {
    console.log('Search data:', { category, date, location })
  }

  return (
    <div className="relative z-20 mx-auto -mt-16 max-w-5xl px-4">
      <div className="bg-background rounded-xl p-4 shadow-lg">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div className="relative">
            <Select onValueChange={setCategory}>
              <SelectTrigger className="border-border bg-background text-muted-foreground hover:bg-muted flex h-10 w-full rounded-md border pl-10 text-sm font-normal outline-none transition-colors focus:outline-none focus:ring-0">
                <SelectValue placeholder="Thể loại" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="portrait">Chụp chân dung</SelectItem>
                <SelectItem value="wedding">Chụp cưới</SelectItem>
                <SelectItem value="family">Chụp gia đình</SelectItem>
                <SelectItem value="event">Chụp sự kiện</SelectItem>
                <SelectItem value="product">Chụp sản phẩm</SelectItem>
              </SelectContent>
            </Select>
            <Camera className="text-muted-foreground absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2" />
          </div>

          <div className="relative">
            <Popover>
              <PopoverTrigger asChild>
                <Button className="border-border bg-background text-muted-foreground hover:bg-muted flex h-10 w-full justify-start rounded-md border pl-10 text-start text-sm font-normal transition-colors focus:outline-none">
                  {date ? format(date, 'dd/MM/yyyy', { locale: vi }) : 'Thời gian'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="bg-background border-border w-auto border p-0 shadow-lg">
                <CalendarComponent mode="single" selected={date} onSelect={setDate} initialFocus />
              </PopoverContent>
            </Popover>
            <Calendar className="text-muted-foreground absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2" />
          </div>

          <div className="relative">
            <Select onValueChange={setLocation}>
              <SelectTrigger className="border-border bg-background text-muted-foreground hover:bg-muted flex h-10 w-full rounded-md border pl-10 text-sm font-normal outline-none transition-colors focus:outline-none focus:ring-0">
                <SelectValue placeholder="Địa điểm" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="hanoi">Hà Nội</SelectItem>
                <SelectItem value="hcm">TP. Hồ Chí Minh</SelectItem>
                <SelectItem value="danang">Đà Nẵng</SelectItem>
                <SelectItem value="nhatrang">Nha Trang</SelectItem>
                <SelectItem value="other">Khác</SelectItem>
              </SelectContent>
            </Select>
            <MapPin className="text-muted-foreground absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2" />
          </div>

          <Button
            onClick={handleSearch}
            className="bg-brand-primary text-brand-white hover:bg-brand-primary/90 h-10 rounded-md"
          >
            <Search className="mr-2 h-4 w-4" />
            Đặt lịch
          </Button>
        </div>
      </div>
    </div>
  )
}

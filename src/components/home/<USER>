'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'
import 'swiper/css'
import 'swiper/css/effect-fade'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import { Autoplay, EffectFade, Navigation, Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react'

interface BannerItem {
  id: number
  imageUrl: string
  title: string
  description: string
  discount?: string
  discountPercent?: string
  validUntil?: string
}

const bannerItems: BannerItem[] = [
  {
    id: 2,
    imageUrl: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg',
    title: 'Chụ<PERSON> ảnh chân dung chuyên nghiệp',
    description: 'Tôn vinh vẻ đẹp của bạn qua từng khung hình',
  },
  {
    id: 3,
    imageUrl: 'https://images.pexels.com/photos/3014857/pexels-photo-3014857.jpeg',
    title: '<PERSON>ụ<PERSON> ảnh sự kiện & tiệc cưới',
    description: '<PERSON><PERSON> lại những kho<PERSON>nh khắc quan trọng trong cuộc đời',
  },
]

export default function HeroBanner() {
  return (
    <div className="relative h-[500px] w-full overflow-hidden md:h-[600px]">
      <Swiper
        modules={[Navigation, Pagination, Autoplay, EffectFade]}
        effect="fade"
        fadeEffect={{ crossFade: true }}
        spaceBetween={0}
        slidesPerView={1}
        navigation={{
          prevEl: '.swiper-button-prev',
          nextEl: '.swiper-button-next',
        }}
        pagination={{
          el: '.swiper-pagination',
          clickable: true,
          bulletClass: 'swiper-pagination-bullet bg-white/50 h-3 w-3 rounded-full transition-all',
          bulletActiveClass: 'swiper-pagination-bullet-active scale-125 bg-white',
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        className="h-full w-full"
      >
        {bannerItems.map((item, index) => (
          <SwiperSlide key={item.id} className="relative h-full w-full">
            <Image src={item.imageUrl} alt={item.title} fill className="object-cover" priority={index === 0} />
            <div className="absolute inset-0 flex flex-col justify-center bg-black/50 px-6 md:px-12 lg:px-24">
              <Card className="bg-white/90 p-6 shadow-lg md:max-w-lg">
                <CardContent>
                  <h1 className="mb-4 text-3xl font-bold text-brand-primary md:text-4xl lg:text-5xl">{item.title}</h1>
                  <p className="mb-6 text-lg text-brand-text-secondary md:text-xl">{item.description}</p>
                  {item.discount && (
                    <div className="mb-6 rounded-lg bg-brand-primary p-3 text-center text-white">
                      <div className="font-bold">{item.discount}</div>
                      {item.discountPercent && <div className="text-2xl font-bold">Giảm {item.discountPercent}</div>}
                      {item.validUntil && <div className="text-sm">Đến: {item.validUntil}</div>}
                    </div>
                  )}
                  <Button className="w-full rounded-full bg-brand-accent py-4 hover:bg-brand-accent/90">
                    Đặt lịch ngay
                  </Button>
                </CardContent>
              </Card>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  )
}

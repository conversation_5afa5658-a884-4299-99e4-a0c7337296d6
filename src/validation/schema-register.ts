import { phoneRegExp } from '@/helper/format'
import { z } from 'zod'

const baseObjectSchema = z.object({
  username: z.string().min(2, 'Tên đăng nhập là bắt buộc.').min(2, 'Tên đăng nhập phải có ít nhất 2 ký tự.'),
  birthDate: z
    .string()
    .min(1, '<PERSON><PERSON><PERSON> sinh là bắt buộc.')
    .regex(/^\d{4}-\d{2}-\d{2}$/, '<PERSON><PERSON><PERSON> sinh không hợp lệ.')
    .refine((date) => {
      const birthDate = new Date(date)
      const today = new Date()
      const minDate = new Date('1960-01-01')
      const age = today.getFullYear() - birthDate.getFullYear()
      return birthDate >= minDate && birthDate <= today && age >= 12
    }, '<PERSON><PERSON><PERSON> sinh không hợp lệ hoặc chưa đủ 12 tuổi.'),
  email: z.string().min(1, '<PERSON><PERSON> là bắt buộc.').email('<PERSON><PERSON> không hợp lệ.'),
  phoneNumber: z.string().refine(
    (value) => {
      const digitsOnly = value.replace(/[^\d+]/g, '')
      const isStartWithPlus84 = value.startsWith('+84')
      const requiredLength = isStartWithPlus84 ? 12 : 10

      if (digitsOnly.length === 0) {
        return false // Bắt buộc
      }
      if (digitsOnly.length > 0 && digitsOnly.length < requiredLength) {
        return false // Chưa đủ số
      }
      if (digitsOnly.length > requiredLength) {
        return false // Vượt quá số
      }
      if (!phoneRegExp.test(value)) {
        return false // Không hợp lệ
      }
      return true
    },
    (value) => {
      const digitsOnly = value.replace(/[^\d+]/g, '')
      const isStartWithPlus84 = value.startsWith('+84')
      const requiredLength = isStartWithPlus84 ? 12 : 10

      if (digitsOnly.length === 0) {
        return { message: 'Số điện thoại là bắt buộc' }
      }
      if (digitsOnly.length > 0 && digitsOnly.length < requiredLength) {
        return { message: 'Số điện thoại chưa đủ số' }
      }
      if (digitsOnly.length > requiredLength) {
        return { message: 'Số điện thoại vượt quá số lượng cho phép' }
      }
      if (!phoneRegExp.test(value)) {
        return { message: 'Số điện thoại không hợp lệ. Phải bắt đầu bằng 03x, 05x, 07x, 08x, 09x.' }
      }
      return { message: '' }
    },
  ),
  password: z.string().min(1, 'Mật khẩu là bắt buộc.').min(6, 'Mật khẩu phải có ít nhất 6 ký tự.'),
  confirmPassword: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc.'),
  roleId: z.string().min(1, 'Vai trò là bắt buộc.'),
})

const photographerFields = z.object({
  workplace: z.string().min(1, 'Địa điểm làm việc là bắt buộc.').min(2, 'Địa điểm làm việc phải có ít nhất 2 ký tự.'),
  facebookLink: z
    .string()
    .min(1, 'Link Facebook là bắt buộc.')
    .url('Link Facebook không hợp lệ.')
    .regex(/facebook\.com/, 'Phải là link Facebook hợp lệ.'),
  experience: z
    .string()
    .regex(/^\d+$/, 'Số năm kinh nghiệm phải là một số hợp lệ.')
    .refine((value) => parseInt(value) > 0, 'Số năm kinh nghiệm phải lớn hơn 0.')
    .refine((value) => parseInt(value) <= 40, 'Số năm kinh nghiệm không được vượt quá 40 năm.'),
  equipment: z.string().min(1, 'Mô tả thiết bị là bắt buộc.').min(5, 'Mô tả thiết bị phải có ít nhất 5 ký tự.'),
})

export const baseSchema = baseObjectSchema.refine((data) => data.password === data.confirmPassword, {
  message: 'Mật khẩu xác nhận không khớp.',
  path: ['confirmPassword'],
})

export const photographerSchema = baseObjectSchema
  .merge(photographerFields)
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Mật khẩu xác nhận không khớp.',
    path: ['confirmPassword'],
  })

export type CustomerFormValues = z.infer<typeof baseSchema>
export type PhotographerFormValues = z.infer<typeof photographerSchema>
export type RegisterFormValues = CustomerFormValues | PhotographerFormValues

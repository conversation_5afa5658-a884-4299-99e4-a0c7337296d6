import { z } from 'zod'
export const basicInfoSchema = z.object({
  category: z.string({ required_error: '<PERSON>ui lòng chọn thể loại' }).min(1, '<PERSON>ui lòng chọn thể loại'),
  background: z.string({ required_error: '<PERSON>ui lòng chọn bối cảnh' }).min(1, '<PERSON>ui lòng chọn bối cảnh'),
  location: z.string().min(1, 'Vui lòng nhập địa điểm chụp'),
  detailedLocation: z.string().optional(),
  shootingDate: z.date({ required_error: 'Vui lòng chọn ngày chụp' }),
})

const additionalServicesSchema = z
  .array(
    z.object({
      id: z.string(),
      label: z.string(),
      cost: z.number().optional(),
      description: z.string().optional(),
    }),
  )
  .optional()

export const detailsInfoSchema = z
  .object({
    title: z.string().min(1, '<PERSON><PERSON> lòng nhập tiêu đề buổi chụp'),
    description: z.string().min(1, '<PERSON><PERSON> lòng nhập nội dung buổi chụp'),
    additionalServices: additionalServicesSchema,
    minBudget: z
      .number({
        required_error: 'Vui lòng nhập chi phí tối thiểu',
        invalid_type_error: 'Chi phí phải là số',
      })
      .min(1000, 'Chi phí tối thiểu phải từ 1,000 VNĐ trở lên')
      .max(1000000000, 'Chi phí tối thiểu không được vượt quá 1 tỷ VNĐ')
      .optional(),
    maxBudget: z
      .number({
        required_error: 'Vui lòng nhập chi phí tối đa',
        invalid_type_error: 'Chi phí phải là số',
      })
      .min(1000, 'Chi phí tối đa phải từ 1,000 VNĐ trở lên')
      .max(1000000000, 'Chi phí tối đa không được vượt quá 1 tỷ VNĐ')
      .optional(),
  })
  .refine(
    (data) => {
      if (data.minBudget && data.maxBudget && data.minBudget > data.maxBudget) {
        return false
      }
      return true
    },
    {
      message: 'Chi phí tối thiểu phải nhỏ hơn hoặc bằng chi phí tối đa',
      path: ['maxBudget'],
    },
  )

export type BasicInfoValues = z.infer<typeof basicInfoSchema>
export type DetailsInfoValues = z.infer<typeof detailsInfoSchema>

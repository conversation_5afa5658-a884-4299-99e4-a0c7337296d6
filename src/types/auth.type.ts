import { User } from './user.type'

export interface LoginCredentials {
  email: string
  password: string
}
export interface LoginResponse {
  accessToken: string
  user: User
}

export interface RegisterCredentials {
  account: {
    username: string
    email: string
    password: string
    phoneNumber?: string
    fullname?: string
    gender?: 'MALE' | 'FEMALE' | 'OTHER'
    dateOfBirth?: string
    address?: string
    avatar?: string
    roleId: string
  }
  photographer?: {
    introduction?: string
    yearsOfExperience?: number
    equipment?: string
    hourlyRate?: number
    capturedPhotos?: string
  }
}

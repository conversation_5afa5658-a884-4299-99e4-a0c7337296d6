export enum CURRENTSTEP {
  BasicInfo = 'basicInfo',
  DetailsInfo = 'detailsInfo',
}

export enum PostStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  COMPLETED = 'COMPLETED',
}

export type AdditionalService = {
  id: string
  label: string
  cost?: number
  description?: string
}

interface Photographer {
  accountId?: string
  yearsOfExperience?: number
  introduction?: string | null
  workplace?: string
  facebookLink?: string
  equipment?: string
  hourlyRate?: number | null
  capturedPhotos?: string | null
  account?: {
    id?: string
    username?: string
    phoneNumber?: string
  }
}

export interface PhotographerRequest {
  id?: string
  postId?: string
  photographerId?: string
  requestDate?: string
  status?: 'ACCEPTED' | 'REJECTED' | 'PENDING' | 'null'
  photographer?: Photographer
}

export interface PostDetail {
  id?: string
  slug?: string
  accountId?: string
  contactPhone?: string
  category: string
  background: string
  location: string
  detailedLocation?: string
  shootingDate: string

  title: string
  description: string
  additionalServices?: AdditionalService[]
  minBudget?: number
  maxBudget?: number
  status?: string
  postDate?: string
  PhotographerRequest?: PhotographerRequest[]
}

export interface PostDetailViewType {
  post: PostDetail
  bookingStatus?: string
  isBooked?: boolean
  totalPhotographerRequests?: number
}

export interface PostFormState {
  postDetail: PostDetail
  currentStep: string
}

export interface CategoryOption {
  id: string
  name: string
  image: string
  description?: string
}

export interface BackgroundOption {
  id: string
  name: string
  image: string
  description?: string
}

export const defaultInitState: PostFormState = {
  postDetail: {
    id: '',
    category: '',
    background: '',
    location: '',
    shootingDate: '',
    title: '',
    description: '',
  },
  currentStep: CURRENTSTEP.BasicInfo,
}

export interface PostSortQueryParams {
  search?: string
  limit?: number
  page?: number
  sort?: string
  direction?: 'asc' | 'desc'
  status?: PostStatus | 'ALL'
  accountId?: string
}

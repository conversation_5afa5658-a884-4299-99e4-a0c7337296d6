import { defaultInitState, PostDetail, PostFormState } from '@/types/post-schema.type'
import { create } from 'zustand'

export const usePostFormStore = create<
  PostFormState & {
    setPostData: (data: Partial<PostDetail>) => void
    resetStore: (newId?: string) => void
    setSelectedStep: (step: string) => void
  }
>()((set) => ({
  ...defaultInitState,
  setPostData: (data) =>
    set((state) => ({
      postDetail: { ...state.postDetail, ...data },
    })),
  resetStore: (newId?: string) =>
    set({
      ...defaultInitState,
      postDetail: {
        ...defaultInitState.postDetail,
        id: newId || '',
      },
    }),
  setSelectedStep: (step) => set({ currentStep: step }),
}))

// stores/auth.store.js
'use client'

import { User } from '@/types/user.type'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

interface AuthState {
  jwt: string | null
  user: User | null
  isLoading: boolean
  setJwt: (token: string) => void
  setAuth: (token: string, user: User) => void
  clearJwt: () => void
  setLoading: (loading: boolean) => void
}

export const useUserStore = create<AuthState>()(
  persist(
    (set) => ({
      jwt: null,
      user: null,
      isLoading: true,
      setJwt: (token: string) => set({ jwt: token }),
      setAuth: (token: string, user: User) => set({ jwt: token, user, isLoading: false }),
      clearJwt: () => set({ jwt: null, user: null, isLoading: false }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ user: state.user, jwt: state.jwt }),
    },
  ),
)

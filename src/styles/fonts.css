@font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-BlackItalic.ttf') format('truetype');
    font-weight: 900;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-ExtraBold.ttf') format('truetype');
    font-weight: 800;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-ExtraBoldItalic.ttf') format('truetype');
    font-weight: 800;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-ExtraLight.ttf') format('truetype');
    font-weight: 200;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-ExtraLightItalic.ttf') format('truetype');
    font-weight: 200;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-MediumItalic.ttf') format('truetype');
    font-weight: 500;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-SemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-SemiBoldItalic.ttf') format('truetype');
    font-weight: 600;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'MontserratAlternates';
    src: url('/fonts/MontserratAlternates-ThinItalic.ttf') format('truetype');
    font-weight: 100;
    font-style: italic;
  }